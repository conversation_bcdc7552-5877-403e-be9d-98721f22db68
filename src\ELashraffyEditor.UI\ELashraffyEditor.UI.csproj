<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <LangVersion>latest</LangVersion>
    <AssemblyTitle>ELashrafy Editor</AssemblyTitle>
    <AssemblyDescription>Professional Photo Editing Application</AssemblyDescription>
    <AssemblyCompany>ELashrafy</AssemblyCompany>
    <AssemblyProduct>ELashrafy Editor</AssemblyProduct>
    <Copyright>Copyright © ELashrafy 2025</Copyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <ApplicationIcon>Resources\Images\app-icon.ico</ApplicationIcon>
    <StartupObject>ELashraffyEditor.UI.App</StartupObject>
    <PublishSingleFile>true</PublishSingleFile>
    <SelfContained>true</SelfContained>
    <RuntimeIdentifier>win-x64</RuntimeIdentifier>
    <PublishReadyToRun>true</PublishReadyToRun>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="MaterialDesignThemes" Version="4.9.0" />
    <PackageReference Include="MaterialDesignColors" Version="2.1.4" />
    <PackageReference Include="Microsoft.Web.WebView2" Version="1.0.2045.28" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="7.0.1" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="7.0.0" />
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.2" />
    <PackageReference Include="System.Text.Json" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ELashraffyEditor.Core\ELashraffyEditor.Core.csproj" />
    <ProjectReference Include="..\ELashraffyEditor.Scripting\ELashraffyEditor.Scripting.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Resource Include="Resources\**\*" />
  </ItemGroup>

  <ItemGroup>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Resources\Images\" />
    <Folder Include="Resources\Localization\" />
    <Folder Include="Resources\Themes\" />
    <Folder Include="Views\" />
    <Folder Include="ViewModels\" />
    <Folder Include="Controls\" />
    <Folder Include="Converters\" />
    <Folder Include="Services\" />
  </ItemGroup>

</Project>
