namespace ELashraffyEditor.UI.Services;

/// <summary>
/// واجهة خدمة الحوارات
/// Dialog service interface
/// </summary>
public interface IDialogService
{
    /// <summary>
    /// عرض رسالة معلومات
    /// Show information message
    /// </summary>
    void ShowInformation(string message, string title = "Information");

    /// <summary>
    /// عرض رسالة تحذير
    /// Show warning message
    /// </summary>
    void ShowWarning(string message, string title = "Warning");

    /// <summary>
    /// عرض رسالة خطأ
    /// Show error message
    /// </summary>
    void ShowError(string message, string title = "Error");

    /// <summary>
    /// عرض حوار تأكيد
    /// Show confirmation dialog
    /// </summary>
    bool ShowConfirmation(string message, string title = "Confirmation");

    /// <summary>
    /// عرض حوار اختيار ملف
    /// Show file selection dialog
    /// </summary>
    string? ShowOpenFileDialog(string filter = "", string title = "Open File");

    /// <summary>
    /// عرض حوار اختيار ملفات متعددة
    /// Show multiple file selection dialog
    /// </summary>
    string[]? ShowOpenFilesDialog(string filter = "", string title = "Open Files");

    /// <summary>
    /// عرض حوار حفظ ملف
    /// Show save file dialog
    /// </summary>
    string? ShowSaveFileDialog(string filter = "", string title = "Save File", string defaultFileName = "");

    /// <summary>
    /// عرض حوار اختيار مجلد
    /// Show folder selection dialog
    /// </summary>
    string? ShowFolderDialog(string title = "Select Folder");

    /// <summary>
    /// عرض حوار إدخال نص
    /// Show text input dialog
    /// </summary>
    string? ShowTextInputDialog(string message, string title = "Input", string defaultValue = "");

    /// <summary>
    /// عرض حوار شريط التقدم
    /// Show progress dialog
    /// </summary>
    IProgressDialog ShowProgressDialog(string title, string message, bool canCancel = false);
}

/// <summary>
/// واجهة حوار شريط التقدم
/// Progress dialog interface
/// </summary>
public interface IProgressDialog : IDisposable
{
    /// <summary>
    /// تحديث التقدم
    /// Update progress
    /// </summary>
    void UpdateProgress(int percentage, string message = "");

    /// <summary>
    /// التحقق من الإلغاء
    /// Check if cancelled
    /// </summary>
    bool IsCancelled { get; }

    /// <summary>
    /// إغلاق الحوار
    /// Close dialog
    /// </summary>
    void Close();
}
