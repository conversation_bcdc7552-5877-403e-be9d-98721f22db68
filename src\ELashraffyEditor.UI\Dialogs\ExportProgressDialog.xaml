<Window x:Class="ELashraffyEditor.UI.Dialogs.ExportProgressDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="تصدير الصور / Exporting Photos"
        Height="400" Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Style="{StaticResource MaterialDesignWindow}">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان / Title -->
        <StackPanel Grid.Row="0" Margin="0,0,0,20">
            <materialDesign:PackIcon Kind="Export" 
                                     Width="48" Height="48"
                                     HorizontalAlignment="Center"
                                     Foreground="{DynamicResource PrimaryHueMidBrush}"/>
            
            <TextBlock Text="تصدير الصور / Exporting Photos"
                       Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                       HorizontalAlignment="Center"
                       Margin="0,10,0,0"/>
        </StackPanel>

        <!-- منطقة التقدم / Progress Area -->
        <materialDesign:Card Grid.Row="1" Padding="20">
            <StackPanel>
                <!-- شريط التقدم الرئيسي / Main Progress Bar -->
                <TextBlock Text="التقدم الإجمالي / Overall Progress" 
                           Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                           Margin="0,0,0,10"/>
                
                <ProgressBar x:Name="OverallProgressBar"
                             Height="12"
                             Style="{StaticResource MaterialDesignLinearProgressBar}"
                             Margin="0,0,0,5"/>
                
                <TextBlock x:Name="OverallProgressText"
                           Text="0%"
                           HorizontalAlignment="Center"
                           FontWeight="Medium"/>

                <!-- الملف الحالي / Current File -->
                <TextBlock Text="الملف الحالي / Current File" 
                           Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                           Margin="0,20,0,10"/>
                
                <TextBlock x:Name="CurrentFileText"
                           Text="جاري التحضير... / Preparing..."
                           FontSize="12"
                           Opacity="0.8"
                           TextWrapping="Wrap"
                           Margin="0,0,0,10"/>

                <ProgressBar x:Name="CurrentFileProgressBar"
                             Height="8"
                             Style="{StaticResource MaterialDesignLinearProgressBar}"
                             IsIndeterminate="True"/>

                <!-- الإحصائيات / Statistics -->
                <Grid Margin="0,20,0,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- مكتمل / Completed -->
                    <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                        <materialDesign:PackIcon Kind="CheckCircle" 
                                                 Width="24" Height="24"
                                                 Foreground="{DynamicResource MaterialDesignLightGreen}"/>
                        <TextBlock x:Name="CompletedCountText" 
                                   Text="0"
                                   Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                   HorizontalAlignment="Center"/>
                        <TextBlock Text="مكتمل / Completed" 
                                   FontSize="11" 
                                   HorizontalAlignment="Center"
                                   Opacity="0.7"/>
                    </StackPanel>

                    <!-- فشل / Failed -->
                    <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                        <materialDesign:PackIcon Kind="AlertCircle" 
                                                 Width="24" Height="24"
                                                 Foreground="{DynamicResource MaterialDesignValidationErrorBrush}"/>
                        <TextBlock x:Name="FailedCountText" 
                                   Text="0"
                                   Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                   HorizontalAlignment="Center"/>
                        <TextBlock Text="فشل / Failed" 
                                   FontSize="11" 
                                   HorizontalAlignment="Center"
                                   Opacity="0.7"/>
                    </StackPanel>

                    <!-- المتبقي / Remaining -->
                    <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                        <materialDesign:PackIcon Kind="Clock" 
                                                 Width="24" Height="24"
                                                 Foreground="{DynamicResource MaterialDesignBody}"/>
                        <TextBlock x:Name="RemainingCountText" 
                                   Text="0"
                                   Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                   HorizontalAlignment="Center"/>
                        <TextBlock Text="متبقي / Remaining" 
                                   FontSize="11" 
                                   HorizontalAlignment="Center"
                                   Opacity="0.7"/>
                    </StackPanel>
                </Grid>

                <!-- الوقت المتبقي / Time Remaining -->
                <StackPanel Orientation="Horizontal" 
                            HorizontalAlignment="Center" 
                            Margin="0,15,0,0">
                    <materialDesign:PackIcon Kind="Timer" 
                                             Width="16" Height="16"
                                             VerticalAlignment="Center"
                                             Margin="0,0,5,0"/>
                    <TextBlock x:Name="TimeRemainingText"
                               Text="حساب الوقت المتبقي... / Calculating time remaining..."
                               FontSize="12"
                               VerticalAlignment="Center"/>
                </StackPanel>
            </StackPanel>
        </materialDesign:Card>

        <!-- رسائل السجل / Log Messages -->
        <Expander Grid.Row="2" 
                  Header="سجل التفاصيل / Detailed Log"
                  Margin="0,10,0,0">
            <ScrollViewer Height="100" 
                          VerticalScrollBarVisibility="Auto">
                <TextBlock x:Name="LogTextBlock"
                           FontFamily="Consolas"
                           FontSize="10"
                           Padding="10"
                           Background="{DynamicResource MaterialDesignDarkBackground}"
                           Foreground="{DynamicResource MaterialDesignDarkForeground}"
                           TextWrapping="Wrap"/>
            </ScrollViewer>
        </Expander>

        <!-- أزرار التحكم / Control Buttons -->
        <StackPanel Grid.Row="3" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Right"
                    Margin="0,20,0,0">
            
            <Button x:Name="PauseResumeButton"
                    Content="إيقاف مؤقت / Pause"
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    Margin="0,0,10,0"
                    Click="PauseResumeButton_Click"/>
            
            <Button x:Name="CancelButton"
                    Content="إلغاء / Cancel"
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    Click="CancelButton_Click"/>
            
            <Button x:Name="CloseButton"
                    Content="إغلاق / Close"
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Margin="10,0,0,0"
                    Click="CloseButton_Click"
                    Visibility="Collapsed"/>
        </StackPanel>
    </Grid>
</Window>
