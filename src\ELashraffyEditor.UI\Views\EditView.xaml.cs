using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Shapes;
using ELashraffyEditor.UI.ViewModels;

namespace ELashraffyEditor.UI.Views;

/// <summary>
/// واجهة التحرير
/// Edit view
/// </summary>
public partial class EditView : UserControl
{
    private EditViewModel? _viewModel;

    public EditView()
    {
        InitializeComponent();
        DataContextChanged += OnDataContextChanged;
    }

    private void OnDataContextChanged(object sender, DependencyPropertyChangedEventArgs e)
    {
        if (_viewModel != null)
        {
            _viewModel.PropertyChanged -= ViewModel_PropertyChanged;
        }

        _viewModel = DataContext as EditViewModel;
        
        if (_viewModel != null)
        {
            _viewModel.PropertyChanged += ViewModel_PropertyChanged;
        }
    }

    private void ViewModel_PropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
    {
        if (e.PropertyName == nameof(EditViewModel.Histogram))
        {
            UpdateHistogramDisplay();
        }
    }

    private void UpdateHistogramDisplay()
    {
        if (_viewModel?.Histogram == null) return;

        Dispatcher.Invoke(() =>
        {
            HistogramCanvas.Children.Clear();
            DrawHistogram(_viewModel.Histogram);
        });
    }

    private void DrawHistogram(int[][] histogram)
    {
        try
        {
            var canvasWidth = HistogramCanvas.ActualWidth;
            var canvasHeight = HistogramCanvas.ActualHeight;

            if (canvasWidth <= 0 || canvasHeight <= 0) return;

            // العثور على أعلى قيمة في الهيستوجرام
            // Find maximum value in histogram
            var maxValue = 0;
            for (int channel = 0; channel < 3; channel++)
            {
                for (int i = 0; i < 256; i++)
                {
                    if (histogram[channel][i] > maxValue)
                        maxValue = histogram[channel][i];
                }
            }

            if (maxValue == 0) return;

            // ألوان القنوات (أحمر، أخضر، أزرق)
            // Channel colors (Red, Green, Blue)
            var channelColors = new[]
            {
                Colors.Red,
                Colors.Green,
                Colors.Blue
            };

            // رسم كل قناة
            // Draw each channel
            for (int channel = 0; channel < 3; channel++)
            {
                var pathGeometry = new PathGeometry();
                var pathFigure = new PathFigure
                {
                    StartPoint = new Point(0, canvasHeight)
                };

                var polyLineSegment = new PolyLineSegment();

                // إنشاء نقاط الخط
                // Create line points
                for (int i = 0; i < 256; i++)
                {
                    var x = (i / 255.0) * canvasWidth;
                    var y = canvasHeight - ((histogram[channel][i] / (double)maxValue) * canvasHeight);
                    polyLineSegment.Points.Add(new Point(x, y));
                }

                // إغلاق المسار
                // Close the path
                polyLineSegment.Points.Add(new Point(canvasWidth, canvasHeight));
                pathFigure.Segments.Add(polyLineSegment);
                pathGeometry.Figures.Add(pathFigure);

                // إنشاء المسار
                // Create path
                var path = new Path
                {
                    Data = pathGeometry,
                    Fill = new SolidColorBrush(channelColors[channel]) { Opacity = 0.3 },
                    Stroke = new SolidColorBrush(channelColors[channel]) { Opacity = 0.8 },
                    StrokeThickness = 1
                };

                HistogramCanvas.Children.Add(path);
            }

            // إضافة خطوط الشبكة
            // Add grid lines
            AddGridLines(canvasWidth, canvasHeight);
        }
        catch (Exception ex)
        {
            // تسجيل الخطأ
            // Log error
            System.Diagnostics.Debug.WriteLine($"Error drawing histogram: {ex.Message}");
        }
    }

    private void AddGridLines(double canvasWidth, double canvasHeight)
    {
        // خطوط عمودية
        // Vertical lines
        for (int i = 1; i < 4; i++)
        {
            var x = (i / 4.0) * canvasWidth;
            var line = new Line
            {
                X1 = x,
                Y1 = 0,
                X2 = x,
                Y2 = canvasHeight,
                Stroke = new SolidColorBrush(Colors.Gray) { Opacity = 0.3 },
                StrokeThickness = 0.5
            };
            HistogramCanvas.Children.Add(line);
        }

        // خطوط أفقية
        // Horizontal lines
        for (int i = 1; i < 4; i++)
        {
            var y = (i / 4.0) * canvasHeight;
            var line = new Line
            {
                X1 = 0,
                Y1 = y,
                X2 = canvasWidth,
                Y2 = y,
                Stroke = new SolidColorBrush(Colors.Gray) { Opacity = 0.3 },
                StrokeThickness = 0.5
            };
            HistogramCanvas.Children.Add(line);
        }
    }

    private void HistogramCanvas_SizeChanged(object sender, SizeChangedEventArgs e)
    {
        // إعادة رسم الهيستوجرام عند تغيير الحجم
        // Redraw histogram when size changes
        if (_viewModel?.Histogram != null)
        {
            UpdateHistogramDisplay();
        }
    }

    private void UserControl_Loaded(object sender, RoutedEventArgs e)
    {
        // ربط حدث تغيير حجم الكانفاس
        // Bind canvas size change event
        HistogramCanvas.SizeChanged += HistogramCanvas_SizeChanged;
    }

    private void UserControl_Unloaded(object sender, RoutedEventArgs e)
    {
        // إلغاء ربط الأحداث
        // Unbind events
        if (_viewModel != null)
        {
            _viewModel.PropertyChanged -= ViewModel_PropertyChanged;
        }
        
        HistogramCanvas.SizeChanged -= HistogramCanvas_SizeChanged;
    }
}
