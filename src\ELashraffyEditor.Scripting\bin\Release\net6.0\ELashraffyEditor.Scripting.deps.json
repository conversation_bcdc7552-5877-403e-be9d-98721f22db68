{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {"ELashraffyEditor.Scripting/1.0.0": {"dependencies": {"ELashraffyEditor.Core": "1.0.0", "Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0", "NLua": "1.7.0", "Python.Runtime": "2.7.9", "System.Text.Json": "8.0.0"}, "runtime": {"ELashraffyEditor.Scripting.dll": {}}}, "KeraLua/1.3.4": {"runtime": {"lib/netstandard2.0/KeraLua.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "runtimeTargets": {"runtimes/android-arm/native/liblua54.so": {"rid": "android-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-arm64/native/liblua54.so": {"rid": "android-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-x64/native/liblua54.so": {"rid": "android-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-x86/native/liblua54.so": {"rid": "android-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/ios/native/liblua54.framework/Info.plist": {"rid": "ios", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/ios/native/liblua54.framework/liblua54": {"rid": "ios", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/liblua54.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/maccatalyst/native/liblua54.framework/Info.plist": {"rid": "maccatalyst", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/maccatalyst/native/liblua54.framework/liblua54": {"rid": "maccatalyst", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx/native/liblua54.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/tvos/native/liblua54.framework/Info.plist": {"rid": "tvos", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/tvos/native/liblua54.framework/liblua54": {"rid": "tvos", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm/native/lua54.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "5.4.4.0"}, "runtimes/win-arm64/native/lua54.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "5.4.4.0"}, "runtimes/win-x64/native/lua54.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "5.4.4.0"}, "runtimes/win-x86/native/lua54.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "5.4.4.0"}}}, "Microsoft.Extensions.DependencyInjection/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/7.0.0": {"runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Logging/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Logging.Abstractions/7.0.0": {"runtime": {"lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Options/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Primitives/7.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "NLua/1.7.0": {"dependencies": {"KeraLua": "1.3.4"}, "runtime": {"lib/netstandard2.0/NLua.dll": {"assemblyVersion": "1.7.0.0", "fileVersion": "1.7.0.0"}}}, "Python.Runtime/2.7.9": {}, "SixLabors.Fonts/2.0.0": {"runtime": {"lib/net6.0/SixLabors.Fonts.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.0.0.0"}}}, "SixLabors.ImageSharp/3.0.2": {"runtime": {"lib/net6.0/SixLabors.ImageSharp.dll": {"assemblyVersion": "3.0.0.0", "fileVersion": "3.0.2.0"}}}, "SixLabors.ImageSharp.Drawing/2.0.1": {"dependencies": {"SixLabors.Fonts": "2.0.0", "SixLabors.ImageSharp": "3.0.2"}, "runtime": {"lib/net6.0/SixLabors.ImageSharp.Drawing.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.0.1.0"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Text.Encodings.Web/8.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "runtimeTargets": {"runtimes/browser/lib/net6.0/System.Text.Encodings.Web.dll": {"rid": "browser", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Text.Json/8.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "8.0.0"}, "runtime": {"lib/net6.0/System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "ELashraffyEditor.Core/1.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0", "SixLabors.ImageSharp": "3.0.2", "SixLabors.ImageSharp.Drawing": "2.0.1", "System.Text.Json": "8.0.0"}, "runtime": {"ELashraffyEditor.Core.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}}}, "libraries": {"ELashraffyEditor.Scripting/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "KeraLua/1.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-FeCNVmVkx/xfg2Q9fIOn9G6bs1W8oaMxLn9CTC1mwnYGQozWJAPl0jfZNW5V+kVaOXWNmn8zBxCQA/7j6to5nw==", "path": "keralua/1.3.4", "hashPath": "keralua.1.3.4.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-elNeOmkeX3eDVG6pYVeV82p29hr+UKDaBhrZyWvWLw/EVZSYEkZlQdkp0V39k/Xehs2Qa0mvoCvkVj3eQxNQ1Q==", "path": "microsoft.extensions.dependencyinjection/7.0.0", "hashPath": "microsoft.extensions.dependencyinjection.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-h3j/QfmFN4S0w4C2A6X7arXij/M/OVw3uQHSOFxnND4DyAzO1F9eMX7Eti7lU/OkSthEE0WzRsfT/Dmx86jzCw==", "path": "microsoft.extensions.dependencyinjection.abstractions/7.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nw2muoNrOG5U5qa2ZekXwudUn2BJcD41e65zwmDHb1fQegTX66UokLWZkJRpqSSHXDOWZ5V0iqhbxOEky91atA==", "path": "microsoft.extensions.logging/7.0.0", "hashPath": "microsoft.extensions.logging.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-kmn78+LPVMOWeITUjIlfxUPDsI0R6G0RkeAMBmQxAJ7vBJn4q2dTva7pWi65ceN5vPGjJ9q/Uae2WKgvfktJAw==", "path": "microsoft.extensions.logging.abstractions/7.0.0", "hashPath": "microsoft.extensions.logging.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lP1yBnTTU42cKpMozuafbvNtQ7QcBjr/CcK3bYOGEMH55Fjt+iecXjT6chR7vbgCMqy3PG3aNQSZgo/EuY/9qQ==", "path": "microsoft.extensions.options/7.0.0", "hashPath": "microsoft.extensions.options.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-um1KU5kxcRp3CNuI8o/GrZtD4AIOXDk+RLsytjZ9QPok3ttLUelLKpilVPuaFT3TFjOhSibUAso0odbOaCDj3Q==", "path": "microsoft.extensions.primitives/7.0.0", "hashPath": "microsoft.extensions.primitives.7.0.0.nupkg.sha512"}, "NLua/1.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-Rks4T8MuD6k/qJ1VCaapSDYmcPbhJ0AeSTBU2hOForytwawRA8QjRwi9xjHvW22A+gQxKF57gMjxhknvTA6uvw==", "path": "nlua/1.7.0", "hashPath": "nlua.1.7.0.nupkg.sha512"}, "Python.Runtime/2.7.9": {"type": "package", "serviceable": true, "sha512": "sha512-NBS5t9KJvBVDoAFsVOQ79Ri133Hir8O6sqcEZJufXLVvlReXvzyZKhkcO5fpWcqjj6798qrWd/tO/TXCx2Ga7g==", "path": "python.runtime/2.7.9", "hashPath": "python.runtime.2.7.9.nupkg.sha512"}, "SixLabors.Fonts/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7kQOwkob8OBTmUWXGUBZuLvRC436IChLzXX0oF3ak+YqXwQDAs9MpuKdvujygcVhs8RyykBSpBpReCi5cQG+iA==", "path": "sixlabors.fonts/2.0.0", "hashPath": "sixlabors.fonts.2.0.0.nupkg.sha512"}, "SixLabors.ImageSharp/3.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-PlwUHnLZ67G1i0xXDYQJYbFiJsvXiEFWtKdV0QvY9j+dFasYbl+vWXXY42/asTsNjchsH5BLuoRzlf8Fa8lzpg==", "path": "sixlabors.imagesharp/3.0.2", "hashPath": "sixlabors.imagesharp.3.0.2.nupkg.sha512"}, "SixLabors.ImageSharp.Drawing/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-zTusvJi0hzu51EYVOa0IupIrem6HQOLXYAbTmmvfqfwU/MZD7ku9BG5SKghQjm8HKAyQxdgrt3udeuHAzPVypQ==", "path": "sixlabors.imagesharp.drawing/2.0.1", "hashPath": "sixlabors.imagesharp.drawing.2.0.1.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Text.Encodings.Web/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ==", "path": "system.text.encodings.web/8.0.0", "hashPath": "system.text.encodings.web.8.0.0.nupkg.sha512"}, "System.Text.Json/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-<PERSON>drZ<PERSON>2WjkiEG6ajEFRABTRCi/wuXQPxeV6g8xvUJqdxMvvuCCEk86zPla8UiIQJz3durtUEbNyY/3lIhS0yZvQ==", "path": "system.text.json/8.0.0", "hashPath": "system.text.json.8.0.0.nupkg.sha512"}, "ELashraffyEditor.Core/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}