<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <LangVersion>latest</LangVersion>
    <AssemblyTitle>ELashrafy Editor Core</AssemblyTitle>
    <AssemblyDescription>Core image processing and business logic for ELashrafy Editor</AssemblyDescription>
    <AssemblyCompany>ELashrafy</AssemblyCompany>
    <AssemblyProduct>ELashrafy Editor</AssemblyProduct>
    <Copyright>Copyright © ELashrafy 2025</Copyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="SixLabors.ImageSharp" Version="3.0.2" />
    <PackageReference Include="SixLabors.ImageSharp.Drawing" Version="2.0.1" />
    <PackageReference Include="System.Text.Json" Version="7.0.3" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="7.0.0" />
  </ItemGroup>

</Project>
