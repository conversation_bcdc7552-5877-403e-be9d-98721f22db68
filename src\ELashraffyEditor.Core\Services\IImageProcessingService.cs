using ELashraffyEditor.Core.Models;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.PixelFormats;

namespace ELashraffyEditor.Core.Services;

/// <summary>
/// واجهة خدمة معالجة الصور
/// Image processing service interface
/// </summary>
public interface IImageProcessingService
{
    /// <summary>
    /// تحميل صورة من مسار الملف
    /// Load image from file path
    /// </summary>
    Task<Image<Rgba32>?> LoadImageAsync(string filePath);

    /// <summary>
    /// حفظ صورة إلى مسار الملف
    /// Save image to file path
    /// </summary>
    Task<bool> SaveImageAsync(Image<Rgba32> image, string filePath, string format = "jpeg", int quality = 95);

    /// <summary>
    /// إنشاء صورة مصغرة
    /// Generate thumbnail
    /// </summary>
    Task<byte[]?> GenerateThumbnailAsync(string filePath, int maxWidth = 200, int maxHeight = 200);

    /// <summary>
    /// تطبيق التعديلات على الصورة
    /// Apply adjustments to image
    /// </summary>
    Task<Image<Rgba32>?> ApplyAdjustmentsAsync(Image<Rgba32> originalImage, ImageAdjustment adjustments);

    /// <summary>
    /// الحصول على معلومات الصورة
    /// Get image information
    /// </summary>
    Task<PhotoItem?> GetImageInfoAsync(string filePath);

    /// <summary>
    /// إنشاء هيستوجرام للصورة
    /// Generate histogram for image
    /// </summary>
    Task<int[][]> GenerateHistogramAsync(Image<Rgba32> image);

    /// <summary>
    /// تطبيق فلتر مخصص
    /// Apply custom filter
    /// </summary>
    Task<Image<Rgba32>?> ApplyCustomFilterAsync(Image<Rgba32> image, string filterName, Dictionary<string, object> parameters);

    /// <summary>
    /// تحسين تلقائي للصورة
    /// Auto-enhance image
    /// </summary>
    Task<ImageAdjustment> AutoEnhanceAsync(Image<Rgba32> image);

    /// <summary>
    /// تصحيح تلقائي للألوان
    /// Auto color correction
    /// </summary>
    Task<ImageAdjustment> AutoColorCorrectionAsync(Image<Rgba32> image);

    /// <summary>
    /// إزالة الضوضاء
    /// Noise reduction
    /// </summary>
    Task<Image<Rgba32>?> ReduceNoiseAsync(Image<Rgba32> image, float strength = 0.5f);

    /// <summary>
    /// تحسين الحدة
    /// Sharpen image
    /// </summary>
    Task<Image<Rgba32>?> SharpenImageAsync(Image<Rgba32> image, float amount = 1.0f);

    /// <summary>
    /// تغيير حجم الصورة
    /// Resize image
    /// </summary>
    Task<Image<Rgba32>?> ResizeImageAsync(Image<Rgba32> image, int width, int height, bool maintainAspectRatio = true);

    /// <summary>
    /// قص الصورة
    /// Crop image
    /// </summary>
    Task<Image<Rgba32>?> CropImageAsync(Image<Rgba32> image, int x, int y, int width, int height);

    /// <summary>
    /// دوران الصورة
    /// Rotate image
    /// </summary>
    Task<Image<Rgba32>?> RotateImageAsync(Image<Rgba32> image, float degrees);

    /// <summary>
    /// انعكاس الصورة
    /// Flip image
    /// </summary>
    Task<Image<Rgba32>?> FlipImageAsync(Image<Rgba32> image, bool horizontal = true);

    /// <summary>
    /// تحويل إلى أبيض وأسود
    /// Convert to black and white
    /// </summary>
    Task<Image<Rgba32>?> ConvertToBlackAndWhiteAsync(Image<Rgba32> image);

    /// <summary>
    /// تطبيق تأثير سيبيا
    /// Apply sepia effect
    /// </summary>
    Task<Image<Rgba32>?> ApplySepiaAsync(Image<Rgba32> image, float amount = 1.0f);

    /// <summary>
    /// تطبيق تأثير الضبابية
    /// Apply blur effect
    /// </summary>
    Task<Image<Rgba32>?> ApplyBlurAsync(Image<Rgba32> image, float radius = 3.0f);

    /// <summary>
    /// تطبيق تأثير الوهج
    /// Apply glow effect
    /// </summary>
    Task<Image<Rgba32>?> ApplyGlowAsync(Image<Rgba32> image, float radius = 10.0f, float strength = 0.5f);

    /// <summary>
    /// تطبيق تأثير الفينيت
    /// Apply vignette effect
    /// </summary>
    Task<Image<Rgba32>?> ApplyVignetteAsync(Image<Rgba32> image, float strength = 0.5f);

    /// <summary>
    /// الحصول على التنسيقات المدعومة
    /// Get supported formats
    /// </summary>
    string[] GetSupportedFormats();

    /// <summary>
    /// التحقق من دعم التنسيق
    /// Check if format is supported
    /// </summary>
    bool IsFormatSupported(string extension);
}
