using ELashraffyEditor.Core.Models;
using Microsoft.Extensions.Logging;
using NLua;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.PixelFormats;
using System.Diagnostics;

namespace ELashraffyEditor.Scripting.Lua;

/// <summary>
/// خدمة البرمجة النصية بـ Lua
/// Lua scripting service
/// </summary>
public class LuaScriptingService : ILuaScriptingService, IDisposable
{
    private readonly ILogger<LuaScriptingService> _logger;
    private readonly NLua.Lua _lua;
    private readonly Dictionary<string, Delegate> _registeredFunctions;
    private readonly string _scriptsDirectory;
    private TimeSpan _timeout = TimeSpan.FromSeconds(30);
    private bool _fileAccessEnabled = false;
    private bool _networkAccessEnabled = false;
    private bool _disposed = false;

    public event EventHandler<ScriptExecutionEventArgs>? ScriptExecuted;
    public event EventHandler<ScriptErrorEventArgs>? ScriptError;

    public LuaScriptingService(ILogger<LuaScriptingService> logger)
    {
        _logger = logger;
        _lua = new NLua.Lua();
        _registeredFunctions = new Dictionary<string, Delegate>();
        
        // تحديد مجلد السكريبتات
        // Set scripts directory
        _scriptsDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Scripts", "Lua");
        Directory.CreateDirectory(_scriptsDirectory);

        InitializeLuaEnvironment();
        _logger.LogInformation("LuaScriptingService initialized");
    }

    private void InitializeLuaEnvironment()
    {
        try
        {
            // تسجيل الدوال الأساسية
            // Register basic functions
            RegisterBasicFunctions();

            // إعداد بيئة آمنة
            // Setup secure environment
            SetupSecureEnvironment();

            _logger.LogDebug("Lua environment initialized");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize Lua environment");
            throw;
        }
    }

    private void RegisterBasicFunctions()
    {
        // دوال معالجة الصور
        // Image processing functions
        RegisterFunction("adjustBrightness", new Func<float, bool>((value) => {
            // سيتم تنفيذها في السياق المناسب
            // Will be implemented in appropriate context
            return true;
        }));

        RegisterFunction("adjustContrast", new Func<float, bool>((value) => {
            return true;
        }));

        RegisterFunction("adjustSaturation", new Func<float, bool>((value) => {
            return true;
        }));

        // دوال المساعدة
        // Helper functions
        RegisterFunction("log", new Action<string>((message) => {
            _logger.LogInformation("Lua Script: {Message}", message);
        }));

        RegisterFunction("getCurrentTime", new Func<string>(() => {
            return DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        }));
    }

    private void SetupSecureEnvironment()
    {
        // إزالة الدوال الخطيرة
        // Remove dangerous functions
        _lua.DoString(@"
            -- إزالة دوال النظام الخطيرة
            os = nil
            io = nil
            package = nil
            require = nil
            dofile = nil
            loadfile = nil
            
            -- إنشاء بيئة آمنة للطباعة
            function print(...)
                local args = {...}
                local message = ''
                for i, v in ipairs(args) do
                    if i > 1 then message = message .. '\t' end
                    message = message .. tostring(v)
                end
                log(message)
            end
        ");
    }

    public async Task<object?> ExecuteScriptAsync(string script, Dictionary<string, object>? parameters = null)
    {
        if (string.IsNullOrWhiteSpace(script))
            throw new ArgumentException("Script cannot be empty", nameof(script));

        var stopwatch = Stopwatch.StartNew();
        object? result = null;

        try
        {
            // تعيين المعاملات
            // Set parameters
            if (parameters != null)
            {
                foreach (var param in parameters)
                {
                    SetGlobalVariable(param.Key, param.Value);
                }
            }

            // تنفيذ السكريبت مع مهلة زمنية
            // Execute script with timeout
            var task = Task.Run(() => _lua.DoString(script));
            
            if (await Task.WhenAny(task, Task.Delay(_timeout)) == task)
            {
                result = await task;
            }
            else
            {
                throw new TimeoutException($"Script execution timed out after {_timeout.TotalSeconds} seconds");
            }

            stopwatch.Stop();

            // إثارة حدث النجاح
            // Raise success event
            ScriptExecuted?.Invoke(this, new ScriptExecutionEventArgs
            {
                Script = script,
                ExecutionTime = stopwatch.Elapsed,
                Result = result,
                Parameters = parameters
            });

            _logger.LogDebug("Script executed successfully in {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
            return result;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            
            // إثارة حدث الخطأ
            // Raise error event
            ScriptError?.Invoke(this, new ScriptErrorEventArgs
            {
                Script = script,
                Exception = ex,
                ErrorMessage = ex.Message
            });

            _logger.LogError(ex, "Script execution failed");
            throw;
        }
    }

    public async Task<object?> ExecuteScriptFileAsync(string filePath, Dictionary<string, object>? parameters = null)
    {
        if (!File.Exists(filePath))
            throw new FileNotFoundException($"Script file not found: {filePath}");

        var script = await File.ReadAllTextAsync(filePath);
        return await ExecuteScriptAsync(script, parameters);
    }

    public async Task<Image<Rgba32>?> ApplyImageScriptAsync(Image<Rgba32> image, string script)
    {
        try
        {
            // إنشاء wrapper للصورة
            // Create image wrapper
            var imageWrapper = new LuaImageWrapper(image);
            
            var parameters = new Dictionary<string, object>
            {
                { "image", imageWrapper }
            };

            await ExecuteScriptAsync(script, parameters);
            
            // إرجاع الصورة المعدلة
            // Return modified image
            return imageWrapper.GetModifiedImage();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to apply image script");
            return null;
        }
    }

    public async Task<ImageAdjustment?> ApplyAdjustmentScriptAsync(Image<Rgba32> image, string script)
    {
        try
        {
            var adjustment = new ImageAdjustment();
            var imageWrapper = new LuaImageWrapper(image);
            
            var parameters = new Dictionary<string, object>
            {
                { "image", imageWrapper },
                { "adjustment", adjustment }
            };

            await ExecuteScriptAsync(script, parameters);
            
            return adjustment;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to apply adjustment script");
            return null;
        }
    }

    public async Task<string> LoadScriptAsync(string filePath)
    {
        if (!File.Exists(filePath))
            throw new FileNotFoundException($"Script file not found: {filePath}");

        return await File.ReadAllTextAsync(filePath);
    }

    public async Task SaveScriptAsync(string filePath, string script)
    {
        var directory = Path.GetDirectoryName(filePath);
        if (!string.IsNullOrEmpty(directory))
        {
            Directory.CreateDirectory(directory);
        }

        await File.WriteAllTextAsync(filePath, script);
        _logger.LogInformation("Script saved to: {FilePath}", filePath);
    }

    public async Task<bool> ValidateScriptAsync(string script)
    {
        try
        {
            // محاولة تحليل السكريبت دون تنفيذه
            // Try to parse script without executing
            await Task.Run(() => _lua.LoadString(script, "validation"));
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Script validation failed");
            return false;
        }
    }

    public async Task<string[]> GetAvailableScriptsAsync()
    {
        if (!Directory.Exists(_scriptsDirectory))
            return Array.Empty<string>();

        var files = Directory.GetFiles(_scriptsDirectory, "*.lua", SearchOption.AllDirectories);
        return await Task.FromResult(files.Select(Path.GetFileName).ToArray()!);
    }

    public void RegisterFunction(string name, Delegate function)
    {
        try
        {
            _registeredFunctions[name] = function;
            _lua.RegisterFunction(name, function.Target, function.Method);
            _logger.LogDebug("Function registered: {FunctionName}", name);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to register function: {FunctionName}", name);
            throw;
        }
    }

    public void UnregisterFunction(string name)
    {
        if (_registeredFunctions.ContainsKey(name))
        {
            _registeredFunctions.Remove(name);
            _lua[name] = null;
            _logger.LogDebug("Function unregistered: {FunctionName}", name);
        }
    }

    public string[] GetRegisteredFunctions()
    {
        return _registeredFunctions.Keys.ToArray();
    }

    public void SetGlobalVariable(string name, object value)
    {
        _lua[name] = value;
    }

    public T? GetGlobalVariable<T>(string name)
    {
        var value = _lua[name];
        if (value is T result)
            return result;
        
        try
        {
            return (T?)Convert.ChangeType(value, typeof(T));
        }
        catch
        {
            return default;
        }
    }

    public void ClearGlobalVariables()
    {
        // إعادة تهيئة البيئة
        // Reinitialize environment
        _lua.DoString("for k,v in pairs(_G) do if type(v) ~= 'function' then _G[k] = nil end end");
    }

    public void SetTimeout(TimeSpan timeout)
    {
        _timeout = timeout;
        _logger.LogDebug("Script timeout set to: {Timeout}", timeout);
    }

    public void SetFileAccessEnabled(bool enabled)
    {
        _fileAccessEnabled = enabled;
        _logger.LogDebug("File access {Status}", enabled ? "enabled" : "disabled");
    }

    public void SetNetworkAccessEnabled(bool enabled)
    {
        _networkAccessEnabled = enabled;
        _logger.LogDebug("Network access {Status}", enabled ? "enabled" : "disabled");
    }

    public Dictionary<string, object> GetEnvironmentInfo()
    {
        return new Dictionary<string, object>
        {
            { "LuaVersion", _lua.DoString("return _VERSION")[0]?.ToString() ?? "Unknown" },
            { "ScriptsDirectory", _scriptsDirectory },
            { "Timeout", _timeout.TotalSeconds },
            { "FileAccessEnabled", _fileAccessEnabled },
            { "NetworkAccessEnabled", _networkAccessEnabled },
            { "RegisteredFunctions", _registeredFunctions.Count },
            { "MemoryUsage", GC.GetTotalMemory(false) }
        };
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _lua?.Dispose();
            _registeredFunctions.Clear();
            _disposed = true;
            _logger.LogInformation("LuaScriptingService disposed");
        }
    }
}

/// <summary>
/// غلاف Lua للصورة
/// Lua wrapper for image
/// </summary>
public class LuaImageWrapper
{
    private readonly Image<Rgba32> _image;
    private ImageAdjustment _adjustments = new();

    public LuaImageWrapper(Image<Rgba32> image)
    {
        _image = image;
    }

    public int Width => _image.Width;
    public int Height => _image.Height;

    public void AdjustBrightness(float value)
    {
        _adjustments.Brightness = value;
    }

    public void AdjustContrast(float value)
    {
        _adjustments.Contrast = value;
    }

    public void AdjustSaturation(float value)
    {
        _adjustments.Saturation = value;
    }

    public void AdjustExposure(float value)
    {
        _adjustments.Exposure = value;
    }

    public Image<Rgba32> GetModifiedImage()
    {
        // هنا يجب تطبيق التعديلات على الصورة
        // Here adjustments should be applied to the image
        // سيتم تنفيذ ذلك عبر ImageProcessingService
        // This will be implemented via ImageProcessingService
        return _image;
    }

    public ImageAdjustment GetAdjustments()
    {
        return _adjustments;
    }
}
