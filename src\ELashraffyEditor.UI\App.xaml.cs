using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System.Windows;
using ELashraffyEditor.UI.ViewModels;
using ELashraffyEditor.UI.Services;
using ELashraffyEditor.Core.Services;
using System.Globalization;
using System.Threading;

namespace ELashraffyEditor.UI;

/// <summary>
/// تطبيق ELashrafy Editor الرئيسي
/// Main ELashrafy Editor Application
/// </summary>
public partial class App : Application
{
    private IHost? _host;

    protected override void OnStartup(StartupEventArgs e)
    {
        // إعداد الثقافة الافتراضية
        // Set default culture
        SetupCulture();

        // إعداد معالج الاستثناءات
        // Setup exception handlers
        SetupExceptionHandlers();

        // إنشاء وإعداد المضيف
        // Create and configure host
        _host = CreateHostBuilder(e.Args).Build();

        // بدء الخدمات
        // Start services
        _host.Start();

        // إظهار النافذة الرئيسية
        // Show main window
        var mainWindow = _host.Services.GetRequiredService<Views.MainWindow>();
        mainWindow.Show();

        base.OnStartup(e);
    }

    protected override void OnExit(ExitEventArgs e)
    {
        _host?.Dispose();
        base.OnExit(e);
    }

    private static IHostBuilder CreateHostBuilder(string[] args) =>
        Host.CreateDefaultBuilder(args)
            .ConfigureAppConfiguration((context, config) =>
            {
                config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
            })
            .ConfigureServices((context, services) =>
            {
                // تسجيل الخدمات الأساسية
                // Register core services
                services.AddSingleton<IImageProcessingService, ImageProcessingService>();
                services.AddSingleton<IThemeService, ThemeService>();
                services.AddSingleton<ILocalizationService, LocalizationService>();
                services.AddSingleton<ISettingsService, SettingsService>();
                services.AddSingleton<IDialogService, DialogService>();

                // تسجيل ViewModels
                // Register ViewModels
                services.AddTransient<MainWindowViewModel>();
                services.AddTransient<LibraryViewModel>();
                services.AddTransient<EditViewModel>();
                services.AddTransient<ExportViewModel>();
                services.AddTransient<SettingsViewModel>();

                // تسجيل النوافذ
                // Register Windows
                services.AddTransient<Views.MainWindow>();
            })
            .ConfigureLogging((context, logging) =>
            {
                logging.ClearProviders();
                logging.AddConsole();
                logging.AddDebug();
                logging.SetMinimumLevel(LogLevel.Information);
            });

    private void SetupCulture()
    {
        // تحديد الثقافة بناءً على إعدادات النظام أو إعدادات التطبيق
        // Determine culture based on system or app settings
        var cultureName = CultureInfo.CurrentCulture.Name;
        
        // دعم العربية والإنجليزية
        // Support Arabic and English
        if (cultureName.StartsWith("ar"))
        {
            var culture = new CultureInfo("ar-SA");
            Thread.CurrentThread.CurrentCulture = culture;
            Thread.CurrentThread.CurrentUICulture = culture;
            CultureInfo.DefaultThreadCurrentCulture = culture;
            CultureInfo.DefaultThreadCurrentUICulture = culture;
        }
        else
        {
            var culture = new CultureInfo("en-US");
            Thread.CurrentThread.CurrentCulture = culture;
            Thread.CurrentThread.CurrentUICulture = culture;
            CultureInfo.DefaultThreadCurrentCulture = culture;
            CultureInfo.DefaultThreadCurrentUICulture = culture;
        }
    }

    private void SetupExceptionHandlers()
    {
        // معالج الاستثناءات غير المعالجة في UI Thread
        // Unhandled exception handler for UI Thread
        DispatcherUnhandledException += (sender, e) =>
        {
            var logger = _host?.Services.GetService<ILogger<App>>();
            logger?.LogError(e.Exception, "Unhandled exception in UI thread");
            
            MessageBox.Show(
                $"حدث خطأ غير متوقع: {e.Exception.Message}\nUnexpected error occurred: {e.Exception.Message}",
                "ELashrafy Editor - خطأ/Error",
                MessageBoxButton.OK,
                MessageBoxImage.Error);
            
            e.Handled = true;
        };

        // معالج الاستثناءات غير المعالجة في Background Threads
        // Unhandled exception handler for Background Threads
        AppDomain.CurrentDomain.UnhandledException += (sender, e) =>
        {
            var logger = _host?.Services.GetService<ILogger<App>>();
            logger?.LogError(e.ExceptionObject as Exception, "Unhandled exception in background thread");
        };

        // معالج استثناءات Task
        // Task exception handler
        TaskScheduler.UnobservedTaskException += (sender, e) =>
        {
            var logger = _host?.Services.GetService<ILogger<App>>();
            logger?.LogError(e.Exception, "Unobserved task exception");
            e.SetObserved();
        };
    }
}
