using System.Windows;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using Microsoft.Extensions.Logging;
using System.IO;

namespace ELashraffyEditor.UI.Services;

/// <summary>
/// خدمة العلامة التجارية والهوية البصرية
/// Branding and visual identity service
/// </summary>
public interface IBrandingService
{
    /// <summary>
    /// اسم التطبيق
    /// Application name
    /// </summary>
    string ApplicationName { get; }

    /// <summary>
    /// إصدار التطبيق
    /// Application version
    /// </summary>
    string ApplicationVersion { get; }

    /// <summary>
    /// وصف التطبيق
    /// Application description
    /// </summary>
    string ApplicationDescription { get; }

    /// <summary>
    /// معلومات حقوق الطبع والنشر
    /// Copyright information
    /// </summary>
    string CopyrightInfo { get; }

    /// <summary>
    /// شعار التطبيق
    /// Application logo
    /// </summary>
    ImageSource? ApplicationLogo { get; }

    /// <summary>
    /// أيقونة التطبيق
    /// Application icon
    /// </summary>
    ImageSource? ApplicationIcon { get; }

    /// <summary>
    /// ألوان العلامة التجارية
    /// Brand colors
    /// </summary>
    BrandColors BrandColors { get; }

    /// <summary>
    /// خطوط العلامة التجارية
    /// Brand fonts
    /// </summary>
    BrandFonts BrandFonts { get; }

    /// <summary>
    /// تطبيق العلامة التجارية على النافذة
    /// Apply branding to window
    /// </summary>
    void ApplyBrandingToWindow(Window window);

    /// <summary>
    /// الحصول على معلومات التطبيق
    /// Get application information
    /// </summary>
    ApplicationInfo GetApplicationInfo();
}

public class BrandingService : IBrandingService
{
    private readonly ILogger<BrandingService> _logger;
    private readonly Lazy<ImageSource?> _applicationLogo;
    private readonly Lazy<ImageSource?> _applicationIcon;

    public string ApplicationName => "محرر الأشرافي / ELashrafy Editor";
    public string ApplicationVersion => GetApplicationVersion();
    public string ApplicationDescription => "محرر صور احترافي مع دعم كامل للغة العربية / Professional photo editor with full Arabic language support";
    public string CopyrightInfo => $"© {DateTime.Now.Year} ELashrafy. جميع الحقوق محفوظة / All rights reserved.";

    public ImageSource? ApplicationLogo => _applicationLogo.Value;
    public ImageSource? ApplicationIcon => _applicationIcon.Value;

    public BrandColors BrandColors { get; }
    public BrandFonts BrandFonts { get; }

    public BrandingService(ILogger<BrandingService> logger)
    {
        _logger = logger;

        // تهيئة الألوان
        // Initialize colors
        BrandColors = new BrandColors();

        // تهيئة الخطوط
        // Initialize fonts
        BrandFonts = new BrandFonts();

        // تحميل الصور بشكل كسول
        // Lazy load images
        _applicationLogo = new Lazy<ImageSource?>(() => LoadImage("pack://application:,,,/Resources/Images/logo.png"));
        _applicationIcon = new Lazy<ImageSource?>(() => LoadImage("pack://application:,,,/Resources/Images/icon.ico"));

        _logger.LogInformation("BrandingService initialized");
    }

    public void ApplyBrandingToWindow(Window window)
    {
        try
        {
            if (window == null) return;

            // تطبيق الأيقونة
            // Apply icon
            if (ApplicationIcon != null)
            {
                window.Icon = ApplicationIcon;
            }

            // تطبيق العنوان
            // Apply title
            if (string.IsNullOrEmpty(window.Title))
            {
                window.Title = ApplicationName;
            }

            // تطبيق الألوان والستايل
            // Apply colors and styling
            ApplyBrandStyling(window);

            _logger.LogDebug("Applied branding to window: {WindowTitle}", window.Title);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to apply branding to window");
        }
    }

    public ApplicationInfo GetApplicationInfo()
    {
        return new ApplicationInfo
        {
            Name = ApplicationName,
            Version = ApplicationVersion,
            Description = ApplicationDescription,
            Copyright = CopyrightInfo,
            CompanyName = "ELashrafy",
            ProductName = "ELashrafy Editor",
            BuildDate = GetBuildDate(),
            IsDebugBuild = IsDebugBuild()
        };
    }

    private string GetApplicationVersion()
    {
        try
        {
            var assembly = System.Reflection.Assembly.GetExecutingAssembly();
            var version = assembly.GetName().Version;
            return version?.ToString() ?? "*******";
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to get application version");
            return "*******";
        }
    }

    private DateTime GetBuildDate()
    {
        try
        {
            var assembly = System.Reflection.Assembly.GetExecutingAssembly();
            var fileInfo = new FileInfo(assembly.Location);
            return fileInfo.LastWriteTime;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to get build date");
            return DateTime.Now;
        }
    }

    private bool IsDebugBuild()
    {
#if DEBUG
        return true;
#else
        return false;
#endif
    }

    private ImageSource? LoadImage(string uri)
    {
        try
        {
            return new BitmapImage(new Uri(uri, UriKind.RelativeOrAbsolute));
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to load image: {Uri}", uri);
            return null;
        }
    }

    private void ApplyBrandStyling(Window window)
    {
        try
        {
            // تطبيق الخطوط
            // Apply fonts
            window.FontFamily = BrandFonts.PrimaryFont;

            // يمكن إضافة المزيد من التخصيصات هنا
            // More customizations can be added here
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to apply brand styling");
        }
    }
}

/// <summary>
/// ألوان العلامة التجارية
/// Brand colors
/// </summary>
public class BrandColors
{
    // الألوان الأساسية
    // Primary colors
    public Color PrimaryColor { get; } = Color.FromRgb(0x1E, 0x88, 0xE5); // أزرق
    public Color SecondaryColor { get; } = Color.FromRgb(0xFF, 0x9C, 0x00); // برتقالي
    public Color AccentColor { get; } = Color.FromRgb(0x4C, 0xAF, 0x50); // أخضر

    // ألوان النص
    // Text colors
    public Color PrimaryTextColor { get; } = Color.FromRgb(0x21, 0x21, 0x21);
    public Color SecondaryTextColor { get; } = Color.FromRgb(0x75, 0x75, 0x75);
    public Color DisabledTextColor { get; } = Color.FromRgb(0xBD, 0xBD, 0xBD);

    // ألوان الخلفية
    // Background colors
    public Color PrimaryBackgroundColor { get; } = Color.FromRgb(0xFA, 0xFA, 0xFA);
    public Color SecondaryBackgroundColor { get; } = Color.FromRgb(0xFF, 0xFF, 0xFF);
    public Color DarkBackgroundColor { get; } = Color.FromRgb(0x30, 0x30, 0x30);

    // ألوان الحدود
    // Border colors
    public Color BorderColor { get; } = Color.FromRgb(0xE0, 0xE0, 0xE0);
    public Color FocusBorderColor { get; } = Color.FromRgb(0x1E, 0x88, 0xE5);

    // ألوان الحالة
    // Status colors
    public Color SuccessColor { get; } = Color.FromRgb(0x4C, 0xAF, 0x50);
    public Color WarningColor { get; } = Color.FromRgb(0xFF, 0x9C, 0x00);
    public Color ErrorColor { get; } = Color.FromRgb(0xF4, 0x43, 0x36);
    public Color InfoColor { get; } = Color.FromRgb(0x21, 0x96, 0xF3);

    // تحويل إلى Brush
    // Convert to Brush
    public SolidColorBrush PrimaryBrush => new(PrimaryColor);
    public SolidColorBrush SecondaryBrush => new(SecondaryColor);
    public SolidColorBrush AccentBrush => new(AccentColor);
    public SolidColorBrush PrimaryTextBrush => new(PrimaryTextColor);
    public SolidColorBrush SecondaryTextBrush => new(SecondaryTextColor);
    public SolidColorBrush DisabledTextBrush => new(DisabledTextColor);
    public SolidColorBrush PrimaryBackgroundBrush => new(PrimaryBackgroundColor);
    public SolidColorBrush SecondaryBackgroundBrush => new(SecondaryBackgroundColor);
    public SolidColorBrush DarkBackgroundBrush => new(DarkBackgroundColor);
    public SolidColorBrush BorderBrush => new(BorderColor);
    public SolidColorBrush FocusBorderBrush => new(FocusBorderColor);
    public SolidColorBrush SuccessBrush => new(SuccessColor);
    public SolidColorBrush WarningBrush => new(WarningColor);
    public SolidColorBrush ErrorBrush => new(ErrorColor);
    public SolidColorBrush InfoBrush => new(InfoColor);
}

/// <summary>
/// خطوط العلامة التجارية
/// Brand fonts
/// </summary>
public class BrandFonts
{
    // الخطوط الأساسية
    // Primary fonts
    public FontFamily PrimaryFont { get; }
    public FontFamily SecondaryFont { get; }
    public FontFamily MonospaceFont { get; }

    // خطوط العربية
    // Arabic fonts
    public FontFamily ArabicFont { get; }
    public FontFamily ArabicTitleFont { get; }

    // خطوط الإنجليزية
    // English fonts
    public FontFamily EnglishFont { get; }
    public FontFamily EnglishTitleFont { get; }

    public BrandFonts()
    {
        // تحديد الخطوط المناسبة للعربية والإنجليزية
        // Define appropriate fonts for Arabic and English
        ArabicFont = new FontFamily("Segoe UI, Tahoma, Arial");
        ArabicTitleFont = new FontFamily("Segoe UI Semibold, Tahoma, Arial");
        
        EnglishFont = new FontFamily("Segoe UI, Helvetica, Arial");
        EnglishTitleFont = new FontFamily("Segoe UI Semibold, Helvetica, Arial");

        // الخطوط الافتراضية
        // Default fonts
        PrimaryFont = ArabicFont; // العربية كافتراضي
        SecondaryFont = EnglishFont;
        MonospaceFont = new FontFamily("Consolas, Courier New, monospace");
    }

    /// <summary>
    /// الحصول على الخط المناسب للغة
    /// Get appropriate font for language
    /// </summary>
    public FontFamily GetFontForCulture(System.Globalization.CultureInfo culture)
    {
        if (culture.TwoLetterISOLanguageName == "ar")
        {
            return ArabicFont;
        }
        
        return EnglishFont;
    }

    /// <summary>
    /// الحصول على خط العنوان المناسب للغة
    /// Get appropriate title font for language
    /// </summary>
    public FontFamily GetTitleFontForCulture(System.Globalization.CultureInfo culture)
    {
        if (culture.TwoLetterISOLanguageName == "ar")
        {
            return ArabicTitleFont;
        }
        
        return EnglishTitleFont;
    }
}

/// <summary>
/// معلومات التطبيق
/// Application information
/// </summary>
public class ApplicationInfo
{
    public string Name { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Copyright { get; set; } = string.Empty;
    public string CompanyName { get; set; } = string.Empty;
    public string ProductName { get; set; } = string.Empty;
    public DateTime BuildDate { get; set; }
    public bool IsDebugBuild { get; set; }
}
