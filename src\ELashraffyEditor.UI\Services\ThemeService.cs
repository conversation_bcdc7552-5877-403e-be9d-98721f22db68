using System.Windows;
using MaterialDesignThemes.Wpf;
using Microsoft.Extensions.Logging;

namespace ELashraffyEditor.UI.Services;

/// <summary>
/// خدمة إدارة الثيمات
/// Theme management service
/// </summary>
public class ThemeService : IThemeService
{
    private readonly ILogger<ThemeService> _logger;
    private string _currentTheme = "Dark";
    private readonly string[] _availableThemes = { "Dark", "Light" };

    public event EventHandler<string>? ThemeChanged;

    public ThemeService(ILogger<ThemeService> logger)
    {
        _logger = logger;
    }

    public void SetTheme(string themeName)
    {
        try
        {
            if (!IsThemeSupported(themeName))
            {
                _logger.LogWarning("Unsupported theme: {ThemeName}", themeName);
                return;
            }

            var paletteHelper = new PaletteHelper();
            var theme = paletteHelper.GetTheme();

            // تعيين الثيم الأساسي
            // Set base theme
            theme.SetBaseTheme(themeName == "Dark" ? Theme.Dark : Theme.Light);

            // تعيين الألوان الأساسية والثانوية
            // Set primary and secondary colors
            theme.SetPrimaryColor(System.Windows.Media.Color.FromRgb(103, 58, 183)); // Deep Purple
            theme.SetSecondaryColor(System.Windows.Media.Color.FromRgb(205, 220, 57)); // Lime

            // تطبيق الثيم
            // Apply theme
            paletteHelper.SetTheme(theme);

            // تحديث موارد التطبيق
            // Update application resources
            UpdateApplicationResources(themeName);

            _currentTheme = themeName;
            ThemeChanged?.Invoke(this, themeName);

            _logger.LogInformation("Theme changed to: {ThemeName}", themeName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to set theme: {ThemeName}", themeName);
        }
    }

    public string GetCurrentTheme()
    {
        return _currentTheme;
    }

    public string[] GetAvailableThemes()
    {
        return _availableThemes;
    }

    public bool IsThemeSupported(string themeName)
    {
        return _availableThemes.Contains(themeName, StringComparer.OrdinalIgnoreCase);
    }

    private void UpdateApplicationResources(string themeName)
    {
        try
        {
            var app = Application.Current;
            if (app?.Resources == null) return;

            // تحديث الألوان بناءً على الثيم
            // Update colors based on theme
            if (themeName == "Dark")
            {
                app.Resources["PrimaryBackgroundBrush"] = new System.Windows.Media.SolidColorBrush(
                    System.Windows.Media.Color.FromRgb(30, 30, 30));
                app.Resources["SecondaryBackgroundBrush"] = new System.Windows.Media.SolidColorBrush(
                    System.Windows.Media.Color.FromRgb(45, 45, 48));
                app.Resources["TertiaryBackgroundBrush"] = new System.Windows.Media.SolidColorBrush(
                    System.Windows.Media.Color.FromRgb(62, 62, 66));
                app.Resources["PrimaryTextBrush"] = new System.Windows.Media.SolidColorBrush(
                    System.Windows.Media.Colors.White);
                app.Resources["SecondaryTextBrush"] = new System.Windows.Media.SolidColorBrush(
                    System.Windows.Media.Color.FromRgb(176, 176, 176));
            }
            else // Light theme
            {
                app.Resources["PrimaryBackgroundBrush"] = new System.Windows.Media.SolidColorBrush(
                    System.Windows.Media.Colors.White);
                app.Resources["SecondaryBackgroundBrush"] = new System.Windows.Media.SolidColorBrush(
                    System.Windows.Media.Color.FromRgb(248, 248, 248));
                app.Resources["TertiaryBackgroundBrush"] = new System.Windows.Media.SolidColorBrush(
                    System.Windows.Media.Color.FromRgb(240, 240, 240));
                app.Resources["PrimaryTextBrush"] = new System.Windows.Media.SolidColorBrush(
                    System.Windows.Media.Colors.Black);
                app.Resources["SecondaryTextBrush"] = new System.Windows.Media.SolidColorBrush(
                    System.Windows.Media.Color.FromRgb(96, 96, 96));
            }

            // الألوان المشتركة
            // Common colors
            app.Resources["AccentBrush"] = new System.Windows.Media.SolidColorBrush(
                System.Windows.Media.Color.FromRgb(103, 58, 183));
            app.Resources["AccentHoverBrush"] = new System.Windows.Media.SolidColorBrush(
                System.Windows.Media.Color.FromRgb(124, 77, 255));
            app.Resources["BorderBrush"] = new System.Windows.Media.SolidColorBrush(
                themeName == "Dark" 
                    ? System.Windows.Media.Color.FromRgb(64, 64, 64)
                    : System.Windows.Media.Color.FromRgb(224, 224, 224));
            app.Resources["HighlightBrush"] = new System.Windows.Media.SolidColorBrush(
                System.Windows.Media.Color.FromRgb(33, 150, 243));

            _logger.LogDebug("Application resources updated for theme: {ThemeName}", themeName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update application resources for theme: {ThemeName}", themeName);
        }
    }
}
