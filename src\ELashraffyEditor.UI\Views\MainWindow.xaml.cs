using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;
using ELashraffyEditor.UI.ViewModels;
using ELashraffyEditor.UI.Services;
using System.Diagnostics;
using System.IO;

namespace ELashraffyEditor.UI.Views;

/// <summary>
/// النافذة الرئيسية لمحرر الأشرافي
/// Main window for ELashrafy Editor
/// </summary>
public partial class MainWindow : Window
{
    private readonly IServiceProvider _serviceProvider;
    private readonly MainWindowViewModel _viewModel;
    private readonly DispatcherTimer _statusTimer;
    private Button? _activeNavigationButton;

    public MainWindow(IServiceProvider serviceProvider, MainWindowViewModel viewModel)
    {
        InitializeComponent();
        
        _serviceProvider = serviceProvider;
        _viewModel = viewModel;
        DataContext = _viewModel;

        // إعداد مؤقت الحالة
        // Setup status timer
        _statusTimer = new DispatcherTimer
        {
            Interval = TimeSpan.FromSeconds(1)
        };
        _statusTimer.Tick += StatusTimer_Tick;
        _statusTimer.Start();

        // تعيين الزر النشط الافتراضي
        // Set default active button
        SetActiveNavigationButton(LibraryButton);

        // إعداد الأحداث
        // Setup events
        Loaded += MainWindow_Loaded;
        Closing += MainWindow_Closing;
    }

    private void MainWindow_Loaded(object sender, RoutedEventArgs e)
    {
        // تحديث النص الأولي
        // Update initial text
        UpdateStatusText("تم تحميل التطبيق بنجاح / Application loaded successfully");
        
        // تحديث استخدام الذاكرة
        // Update memory usage
        UpdateMemoryUsage();
    }

    private void MainWindow_Closing(object sender, System.ComponentModel.CancelEventArgs e)
    {
        // إيقاف المؤقت
        // Stop timer
        _statusTimer?.Stop();

        // حفظ الإعدادات
        // Save settings
        var settingsService = _serviceProvider.GetService<ISettingsService>();
        settingsService?.SaveSettings();
    }

    private void StatusTimer_Tick(object? sender, EventArgs e)
    {
        // تحديث الوقت الحالي
        // Update current time
        CurrentTime.Text = DateTime.Now.ToString("HH:mm:ss");
        
        // تحديث استخدام الذاكرة كل 5 ثوانٍ
        // Update memory usage every 5 seconds
        if (DateTime.Now.Second % 5 == 0)
        {
            UpdateMemoryUsage();
        }
    }

    private void UpdateMemoryUsage()
    {
        var process = Process.GetCurrentProcess();
        var memoryMB = process.WorkingSet64 / (1024 * 1024);
        MemoryUsage.Text = $"Memory: {memoryMB} MB";
    }

    private void UpdateStatusText(string message)
    {
        StatusText.Text = message;
    }

    #region Window Controls

    private void MinimizeButton_Click(object sender, RoutedEventArgs e)
    {
        WindowState = WindowState.Minimized;
    }

    private void MaximizeButton_Click(object sender, RoutedEventArgs e)
    {
        WindowState = WindowState == WindowState.Maximized ? WindowState.Normal : WindowState.Maximized;
    }

    private void CloseButton_Click(object sender, RoutedEventArgs e)
    {
        Close();
    }

    #endregion

    #region Navigation

    private void NavigationButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is string section)
        {
            SetActiveNavigationButton(button);
            NavigateToSection(section);
        }
    }

    private void SetActiveNavigationButton(Button button)
    {
        // إزالة التمييز من الزر السابق
        // Remove highlight from previous button
        if (_activeNavigationButton != null)
        {
            _activeNavigationButton.Background = System.Windows.Media.Brushes.Transparent;
            _activeNavigationButton.Foreground = (System.Windows.Media.Brush)FindResource("SecondaryTextBrush");
        }

        // تمييز الزر الحالي
        // Highlight current button
        _activeNavigationButton = button;
        button.Background = (System.Windows.Media.Brush)FindResource("TertiaryBackgroundBrush");
        button.Foreground = (System.Windows.Media.Brush)FindResource("PrimaryTextBrush");
    }

    private void NavigateToSection(string section)
    {
        try
        {
            switch (section)
            {
                case "Library":
                    ShowLibraryView();
                    UpdateStatusText("عرض المكتبة / Library view");
                    break;
                case "Edit":
                    ShowEditView();
                    UpdateStatusText("وضع التحرير / Edit mode");
                    break;
                case "Export":
                    ShowExportView();
                    UpdateStatusText("وضع التصدير / Export mode");
                    break;
                case "Settings":
                    ShowSettingsView();
                    UpdateStatusText("الإعدادات / Settings");
                    break;
                default:
                    UpdateStatusText("قسم غير معروف / Unknown section");
                    break;
            }
        }
        catch (Exception ex)
        {
            UpdateStatusText($"خطأ في التنقل / Navigation error: {ex.Message}");
        }
    }

    private void ShowLibraryView()
    {
        // إنشاء عرض المكتبة
        // Create library view
        var libraryView = new TextBlock
        {
            Text = "عرض المكتبة - قريباً\nLibrary View - Coming Soon",
            HorizontalAlignment = HorizontalAlignment.Center,
            VerticalAlignment = VerticalAlignment.Center,
            FontSize = 18,
            TextAlignment = TextAlignment.Center
        };
        MainContent.Content = libraryView;
    }

    private void ShowEditView()
    {
        // إنشاء عرض التحرير
        // Create edit view
        var editView = new TextBlock
        {
            Text = "وضع التحرير - قريباً\nEdit Mode - Coming Soon",
            HorizontalAlignment = HorizontalAlignment.Center,
            VerticalAlignment = VerticalAlignment.Center,
            FontSize = 18,
            TextAlignment = TextAlignment.Center
        };
        MainContent.Content = editView;
    }

    private void ShowExportView()
    {
        // إنشاء عرض التصدير
        // Create export view
        var exportView = new TextBlock
        {
            Text = "وضع التصدير - قريباً\nExport Mode - Coming Soon",
            HorizontalAlignment = HorizontalAlignment.Center,
            VerticalAlignment = VerticalAlignment.Center,
            FontSize = 18,
            TextAlignment = TextAlignment.Center
        };
        MainContent.Content = exportView;
    }

    private void ShowSettingsView()
    {
        // إنشاء عرض الإعدادات
        // Create settings view
        var settingsView = new TextBlock
        {
            Text = "الإعدادات - قريباً\nSettings - Coming Soon",
            HorizontalAlignment = HorizontalAlignment.Center,
            VerticalAlignment = VerticalAlignment.Center,
            FontSize = 18,
            TextAlignment = TextAlignment.Center
        };
        MainContent.Content = settingsView;
    }

    #endregion

    #region Import Photos

    private void ImportPhotos_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var openFileDialog = new Microsoft.Win32.OpenFileDialog
            {
                Title = "اختر الصور / Select Images",
                Filter = "Image Files|*.jpg;*.jpeg;*.png;*.tiff;*.bmp;*.gif|" +
                        "JPEG Files|*.jpg;*.jpeg|" +
                        "PNG Files|*.png|" +
                        "TIFF Files|*.tiff;*.tif|" +
                        "All Files|*.*",
                Multiselect = true
            };

            if (openFileDialog.ShowDialog() == true)
            {
                var fileCount = openFileDialog.FileNames.Length;
                UpdateStatusText($"تم اختيار {fileCount} ملف / Selected {fileCount} files");
                
                // التبديل إلى عرض المكتبة
                // Switch to library view
                SetActiveNavigationButton(LibraryButton);
                ShowLibraryView();
            }
        }
        catch (Exception ex)
        {
            UpdateStatusText($"خطأ في استيراد الصور / Import error: {ex.Message}");
            MessageBox.Show(
                $"حدث خطأ أثناء استيراد الصور:\n{ex.Message}",
                "خطأ / Error",
                MessageBoxButton.OK,
                MessageBoxImage.Error);
        }
    }

    #endregion
}
