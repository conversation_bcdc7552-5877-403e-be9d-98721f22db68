<UserControl x:Class="ELashraffyEditor.UI.Views.EditView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:viewmodels="clr-namespace:ELashraffyEditor.UI.ViewModels"
             mc:Ignorable="d" 
             d:DesignHeight="800" d:DesignWidth="1200"
             d:DataContext="{d:DesignInstance Type=viewmodels:EditViewModel}">

    <UserControl.Resources>
        <Style x:Key="AdjustmentSliderStyle" TargetType="Slider" BasedOn="{StaticResource MaterialDesignSlider}">
            <Setter Property="Minimum" Value="-100"/>
            <Setter Property="Maximum" Value="100"/>
            <Setter Property="Value" Value="0"/>
            <Setter Property="TickFrequency" Value="10"/>
            <Setter Property="IsSnapToTickEnabled" Value="True"/>
            <Setter Property="Margin" Value="0,5"/>
        </Style>
        
        <Style x:Key="AdjustmentLabelStyle" TargetType="TextBlock" BasedOn="{StaticResource MaterialDesignBody1TextBlock}">
            <Setter Property="Margin" Value="0,10,0,5"/>
            <Setter Property="FontWeight" Value="Medium"/>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="300"/>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="250"/>
        </Grid.ColumnDefinitions>

        <!-- أدوات التحرير / Editing Tools -->
        <materialDesign:Card Grid.Column="0" Margin="10" Padding="15">
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <StackPanel>
                    <TextBlock Text="أدوات التحرير / Editing Tools" 
                               Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                               Margin="0,0,0,20"/>

                    <!-- التعرض / Exposure -->
                    <TextBlock Text="التعرض / Exposure" Style="{StaticResource AdjustmentLabelStyle}"/>
                    <Slider Style="{StaticResource AdjustmentSliderStyle}"
                            Value="{Binding Adjustments.Exposure, Mode=TwoWay}"/>
                    <TextBlock Text="{Binding Adjustments.Exposure, StringFormat='{}{0:F1}'}" 
                               HorizontalAlignment="Center" FontSize="12"/>

                    <!-- السطوع / Brightness -->
                    <TextBlock Text="السطوع / Brightness" Style="{StaticResource AdjustmentLabelStyle}"/>
                    <Slider Style="{StaticResource AdjustmentSliderStyle}"
                            Value="{Binding Adjustments.Brightness, Mode=TwoWay}"/>
                    <TextBlock Text="{Binding Adjustments.Brightness, StringFormat='{}{0:F1}'}" 
                               HorizontalAlignment="Center" FontSize="12"/>

                    <!-- التباين / Contrast -->
                    <TextBlock Text="التباين / Contrast" Style="{StaticResource AdjustmentLabelStyle}"/>
                    <Slider Style="{StaticResource AdjustmentSliderStyle}"
                            Value="{Binding Adjustments.Contrast, Mode=TwoWay}"/>
                    <TextBlock Text="{Binding Adjustments.Contrast, StringFormat='{}{0:F1}'}" 
                               HorizontalAlignment="Center" FontSize="12"/>

                    <!-- التشبع / Saturation -->
                    <TextBlock Text="التشبع / Saturation" Style="{StaticResource AdjustmentLabelStyle}"/>
                    <Slider Style="{StaticResource AdjustmentSliderStyle}"
                            Value="{Binding Adjustments.Saturation, Mode=TwoWay}"/>
                    <TextBlock Text="{Binding Adjustments.Saturation, StringFormat='{}{0:F1}'}" 
                               HorizontalAlignment="Center" FontSize="12"/>

                    <!-- الحيوية / Vibrance -->
                    <TextBlock Text="الحيوية / Vibrance" Style="{StaticResource AdjustmentLabelStyle}"/>
                    <Slider Style="{StaticResource AdjustmentSliderStyle}"
                            Value="{Binding Adjustments.Vibrance, Mode=TwoWay}"/>
                    <TextBlock Text="{Binding Adjustments.Vibrance, StringFormat='{}{0:F1}'}" 
                               HorizontalAlignment="Center" FontSize="12"/>

                    <!-- الظلال / Shadows -->
                    <TextBlock Text="الظلال / Shadows" Style="{StaticResource AdjustmentLabelStyle}"/>
                    <Slider Style="{StaticResource AdjustmentSliderStyle}"
                            Value="{Binding Adjustments.Shadows, Mode=TwoWay}"/>
                    <TextBlock Text="{Binding Adjustments.Shadows, StringFormat='{}{0:F1}'}" 
                               HorizontalAlignment="Center" FontSize="12"/>

                    <!-- الإضاءات العالية / Highlights -->
                    <TextBlock Text="الإضاءات العالية / Highlights" Style="{StaticResource AdjustmentLabelStyle}"/>
                    <Slider Style="{StaticResource AdjustmentSliderStyle}"
                            Value="{Binding Adjustments.Highlights, Mode=TwoWay}"/>
                    <TextBlock Text="{Binding Adjustments.Highlights, StringFormat='{}{0:F1}'}" 
                               HorizontalAlignment="Center" FontSize="12"/>

                    <!-- الوضوح / Clarity -->
                    <TextBlock Text="الوضوح / Clarity" Style="{StaticResource AdjustmentLabelStyle}"/>
                    <Slider Style="{StaticResource AdjustmentSliderStyle}"
                            Value="{Binding Adjustments.Clarity, Mode=TwoWay}"/>
                    <TextBlock Text="{Binding Adjustments.Clarity, StringFormat='{}{0:F1}'}" 
                               HorizontalAlignment="Center" FontSize="12"/>

                    <!-- الحدة / Sharpness -->
                    <TextBlock Text="الحدة / Sharpness" Style="{StaticResource AdjustmentLabelStyle}"/>
                    <Slider Style="{StaticResource AdjustmentSliderStyle}"
                            Value="{Binding Adjustments.Sharpness, Mode=TwoWay}"/>
                    <TextBlock Text="{Binding Adjustments.Sharpness, StringFormat='{}{0:F1}'}" 
                               HorizontalAlignment="Center" FontSize="12"/>

                    <!-- أزرار التحكم / Control Buttons -->
                    <StackPanel Orientation="Horizontal" Margin="0,20,0,0" HorizontalAlignment="Center">
                        <Button Content="تحسين تلقائي / Auto Enhance" 
                                Command="{Binding AutoEnhanceCommand}"
                                Style="{StaticResource MaterialDesignRaisedButton}"
                                Margin="5"/>
                        <Button Content="إعادة تعيين / Reset" 
                                Command="{Binding ResetAdjustmentsCommand}"
                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                Margin="5"/>
                    </StackPanel>
                </StackPanel>
            </ScrollViewer>
        </materialDesign:Card>

        <!-- منطقة عرض الصورة / Image Display Area -->
        <materialDesign:Card Grid.Column="1" Margin="10">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- شريط الأدوات العلوي / Top Toolbar -->
                <ToolBar Grid.Row="0" Style="{StaticResource MaterialDesignToolBar}">
                    <Button ToolTip="تكبير / Zoom In" Command="{Binding ZoomInCommand}">
                        <materialDesign:PackIcon Kind="MagnifyPlus"/>
                    </Button>
                    <Button ToolTip="تصغير / Zoom Out" Command="{Binding ZoomOutCommand}">
                        <materialDesign:PackIcon Kind="MagnifyMinus"/>
                    </Button>
                    <Button ToolTip="ملائمة الشاشة / Fit to Screen" Command="{Binding ZoomFitCommand}">
                        <materialDesign:PackIcon Kind="FitToPage"/>
                    </Button>
                    <Button ToolTip="الحجم الفعلي / Actual Size" Command="{Binding ZoomActualSizeCommand}">
                        <materialDesign:PackIcon Kind="Magnify"/>
                    </Button>
                    <Separator/>
                    <ToggleButton ToolTip="مقارنة قبل/بعد / Before/After" 
                                  IsChecked="{Binding ShowBeforeAfter, Mode=TwoWay}">
                        <materialDesign:PackIcon Kind="CompareHorizontal"/>
                    </ToggleButton>
                </ToolBar>

                <!-- منطقة عرض الصورة / Image Viewer -->
                <ScrollViewer Grid.Row="1" HorizontalScrollBarVisibility="Auto" 
                              VerticalScrollBarVisibility="Auto" Background="{DynamicResource MaterialDesignDarkBackground}">
                    <Grid>
                        <!-- الصورة الأصلية / Original Image -->
                        <Image Source="{Binding PreviewImageData}" 
                               Stretch="Uniform"
                               Visibility="{Binding ShowBeforeAfter, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                        
                        <!-- مقارنة قبل/بعد / Before/After Comparison -->
                        <Grid Visibility="{Binding ShowBeforeAfter, Converter={StaticResource InverseBooleanToVisibilityConverter}}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <Image Grid.Column="0" Source="{Binding OriginalImageData}" Stretch="Uniform"/>
                            <Image Grid.Column="1" Source="{Binding PreviewImageData}" Stretch="Uniform"/>
                            
                            <TextBlock Grid.Column="0" Text="قبل / Before" 
                                       VerticalAlignment="Top" HorizontalAlignment="Center"
                                       Background="{DynamicResource MaterialDesignPaper}"
                                       Padding="10,5" Margin="10"/>
                            <TextBlock Grid.Column="1" Text="بعد / After" 
                                       VerticalAlignment="Top" HorizontalAlignment="Center"
                                       Background="{DynamicResource MaterialDesignPaper}"
                                       Padding="10,5" Margin="10"/>
                        </Grid>
                    </Grid>
                </ScrollViewer>

                <!-- شريط الحالة السفلي / Bottom Status Bar -->
                <StatusBar Grid.Row="2">
                    <StatusBarItem>
                        <TextBlock Text="{Binding ZoomLevel}"/>
                    </StatusBarItem>
                    <Separator/>
                    <StatusBarItem>
                        <TextBlock Text="{Binding CurrentPhoto.FileName}"/>
                    </StatusBarItem>
                    <StatusBarItem HorizontalAlignment="Right">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Loading" 
                                                     Visibility="{Binding IsProcessing, Converter={StaticResource BooleanToVisibilityConverter}}"
                                                     Margin="0,0,5,0"/>
                            <TextBlock Text="جاري المعالجة... / Processing..." 
                                       Visibility="{Binding IsProcessing, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                        </StackPanel>
                    </StatusBarItem>
                </StatusBar>
            </Grid>
        </materialDesign:Card>

        <!-- الهيستوجرام ومعلومات الصورة / Histogram and Image Info -->
        <materialDesign:Card Grid.Column="2" Margin="10" Padding="15">
            <StackPanel>
                <TextBlock Text="الهيستوجرام / Histogram" 
                           Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                           Margin="0,0,0,10"/>

                <!-- عرض الهيستوجرام / Histogram Display -->
                <Border Height="150" Background="{DynamicResource MaterialDesignDarkBackground}"
                        BorderBrush="{DynamicResource MaterialDesignDivider}" BorderThickness="1">
                    <Canvas Name="HistogramCanvas" Margin="5">
                        <!-- سيتم رسم الهيستوجرام هنا برمجياً -->
                        <!-- Histogram will be drawn here programmatically -->
                    </Canvas>
                </Border>

                <!-- معلومات الصورة / Image Information -->
                <TextBlock Text="معلومات الصورة / Image Info" 
                           Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                           Margin="0,20,0,10"/>

                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Grid.Column="0" Text="الأبعاد / Dimensions:" Margin="0,2"/>
                    <TextBlock Grid.Row="0" Grid.Column="1" 
                               Text="{Binding CurrentPhoto.Width, StringFormat='{}{0} x '}"
                               Margin="5,2"/>

                    <TextBlock Grid.Row="1" Grid.Column="0" Text="الحجم / Size:" Margin="0,2"/>
                    <TextBlock Grid.Row="1" Grid.Column="1" 
                               Text="{Binding CurrentPhoto.FileSize, Converter={StaticResource FileSizeConverter}}"
                               Margin="5,2"/>

                    <TextBlock Grid.Row="2" Grid.Column="0" Text="التنسيق / Format:" Margin="0,2"/>
                    <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding CurrentPhoto.Format}" Margin="5,2"/>

                    <TextBlock Grid.Row="3" Grid.Column="0" Text="تاريخ الإنشاء / Created:" Margin="0,2"/>
                    <TextBlock Grid.Row="3" Grid.Column="1" 
                               Text="{Binding CurrentPhoto.DateCreated, StringFormat='{}{0:dd/MM/yyyy}'}"
                               Margin="5,2"/>

                    <TextBlock Grid.Row="4" Grid.Column="0" Text="تاريخ التعديل / Modified:" Margin="0,2"/>
                    <TextBlock Grid.Row="4" Grid.Column="1" 
                               Text="{Binding CurrentPhoto.DateModified, StringFormat='{}{0:dd/MM/yyyy}'}"
                               Margin="5,2"/>
                </Grid>

                <!-- أزرار الحفظ والتصدير / Save and Export Buttons -->
                <StackPanel Margin="0,20,0,0">
                    <Button Content="حفظ التعديلات / Save Changes" 
                            Command="{Binding SaveChangesCommand}"
                            Style="{StaticResource MaterialDesignRaisedButton}"
                            Margin="0,5"
                            IsEnabled="{Binding HasUnsavedChanges}"/>
                    
                    <Button Content="تصدير الصورة / Export Image" 
                            Command="{Binding ExportPhotoCommand}"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Margin="0,5"/>
                </StackPanel>
            </StackPanel>
        </materialDesign:Card>
    </Grid>
</UserControl>
