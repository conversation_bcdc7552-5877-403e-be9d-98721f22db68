// Main JavaScript for ELashrafy Editor Settings
class ELashraffySettings {
    constructor() {
        this.currentLanguage = 'ar-SA';
        this.currentTheme = 'dark';
        this.settings = {};
        
        this.init();
    }

    init() {
        this.loadSettings();
        this.setupEventListeners();
        this.setupNavigation();
        this.setupSliders();
        this.updateUI();
    }

    setupEventListeners() {
        // Theme toggle
        const themeBtn = document.getElementById('themeBtn');
        if (themeBtn) {
            themeBtn.addEventListener('click', () => this.toggleTheme());
        }

        // Language toggle
        const languageBtn = document.getElementById('languageBtn');
        if (languageBtn) {
            languageBtn.addEventListener('click', () => this.toggleLanguage());
        }

        // Save button
        const saveBtn = document.getElementById('saveBtn');
        if (saveBtn) {
            saveBtn.addEventListener('click', () => this.saveSettings());
        }

        // Reset button
        const resetBtn = document.getElementById('resetBtn');
        if (resetBtn) {
            resetBtn.addEventListener('click', () => this.resetSettings());
        }

        // Form controls
        this.setupFormControls();
    }

    setupNavigation() {
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const section = link.getAttribute('data-section');
                this.showSection(section);
                
                // Update active nav item
                document.querySelectorAll('.nav-item').forEach(item => item.classList.remove('active'));
                link.closest('.nav-item').classList.add('active');
            });
        });
    }

    setupSliders() {
        const sliders = document.querySelectorAll('.slider');
        sliders.forEach(slider => {
            const updateValue = () => {
                const valueSpan = slider.nextElementSibling;
                if (valueSpan && valueSpan.classList.contains('slider-value')) {
                    let value = slider.value;
                    if (slider.id === 'thumbnailSize') {
                        valueSpan.textContent = `${value}px`;
                    } else if (slider.id === 'defaultQuality') {
                        valueSpan.textContent = `${value}%`;
                    } else {
                        valueSpan.textContent = value;
                    }
                }
            };

            slider.addEventListener('input', updateValue);
            updateValue(); // Initial update
        });
    }

    setupFormControls() {
        // Language selector
        const languageSelect = document.getElementById('language');
        if (languageSelect) {
            languageSelect.addEventListener('change', (e) => {
                this.currentLanguage = e.target.value;
                this.updateLanguage();
            });
        }

        // Theme selector
        const themeSelect = document.getElementById('theme');
        if (themeSelect) {
            themeSelect.addEventListener('change', (e) => {
                this.currentTheme = e.target.value;
                this.applyTheme();
            });
        }

        // All other form controls
        const formControls = document.querySelectorAll('input, select');
        formControls.forEach(control => {
            control.addEventListener('change', () => {
                this.markAsModified();
            });
        });
    }

    showSection(sectionId) {
        // Hide all sections
        document.querySelectorAll('.settings-section').forEach(section => {
            section.classList.remove('active');
        });

        // Show target section
        const targetSection = document.getElementById(sectionId);
        if (targetSection) {
            targetSection.classList.add('active');
        }
    }

    toggleTheme() {
        this.currentTheme = this.currentTheme === 'dark' ? 'light' : 'dark';
        this.applyTheme();
        
        // Update theme selector
        const themeSelect = document.getElementById('theme');
        if (themeSelect) {
            themeSelect.value = this.currentTheme;
        }
    }

    applyTheme() {
        const body = document.body;
        const themeBtn = document.getElementById('themeBtn');
        
        if (this.currentTheme === 'dark') {
            body.classList.remove('light-theme');
            body.classList.add('dark-theme');
            if (themeBtn) {
                themeBtn.querySelector('.material-icons').textContent = 'dark_mode';
            }
        } else {
            body.classList.remove('dark-theme');
            body.classList.add('light-theme');
            if (themeBtn) {
                themeBtn.querySelector('.material-icons').textContent = 'light_mode';
            }
        }

        // Notify parent application
        this.notifyParent('themeChanged', { theme: this.currentTheme });
    }

    toggleLanguage() {
        this.currentLanguage = this.currentLanguage === 'ar-SA' ? 'en-US' : 'ar-SA';
        this.updateLanguage();
        
        // Update language selector
        const languageSelect = document.getElementById('language');
        if (languageSelect) {
            languageSelect.value = this.currentLanguage;
        }
    }

    updateLanguage() {
        const html = document.documentElement;
        const isArabic = this.currentLanguage.startsWith('ar');
        
        html.setAttribute('lang', this.currentLanguage);
        html.setAttribute('dir', isArabic ? 'rtl' : 'ltr');
        
        // Update font family
        document.body.style.fontFamily = isArabic ? 
            "var(--font-arabic)" : "var(--font-english)";

        // Notify parent application
        this.notifyParent('languageChanged', { language: this.currentLanguage });
    }

    loadSettings() {
        // Try to get settings from parent application
        if (window.chrome && window.chrome.webview) {
            window.chrome.webview.postMessage({
                type: 'getSettings'
            });
        } else {
            // Fallback to localStorage for testing
            const savedSettings = localStorage.getItem('elashraffySettings');
            if (savedSettings) {
                this.settings = JSON.parse(savedSettings);
                this.applyLoadedSettings();
            }
        }
    }

    applyLoadedSettings() {
        // Apply theme
        if (this.settings.theme) {
            this.currentTheme = this.settings.theme;
            this.applyTheme();
        }

        // Apply language
        if (this.settings.language) {
            this.currentLanguage = this.settings.language;
            this.updateLanguage();
        }

        // Apply form values
        Object.keys(this.settings).forEach(key => {
            const element = document.getElementById(key);
            if (element) {
                if (element.type === 'checkbox') {
                    element.checked = this.settings[key];
                } else {
                    element.value = this.settings[key];
                }
            }
        });

        // Update sliders
        this.setupSliders();
    }

    saveSettings() {
        this.collectSettings();
        
        // Send to parent application
        if (window.chrome && window.chrome.webview) {
            window.chrome.webview.postMessage({
                type: 'saveSettings',
                data: this.settings
            });
        } else {
            // Fallback to localStorage for testing
            localStorage.setItem('elashraffySettings', JSON.stringify(this.settings));
        }

        this.showNotification('تم حفظ الإعدادات بنجاح / Settings saved successfully', 'success');
    }

    collectSettings() {
        this.settings = {
            theme: this.currentTheme,
            language: this.currentLanguage
        };

        // Collect all form values
        const formControls = document.querySelectorAll('input, select');
        formControls.forEach(control => {
            if (control.id) {
                if (control.type === 'checkbox') {
                    this.settings[control.id] = control.checked;
                } else {
                    this.settings[control.id] = control.value;
                }
            }
        });
    }

    resetSettings() {
        if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟\nAre you sure you want to reset all settings?')) {
            // Reset to defaults
            this.settings = {
                theme: 'dark',
                language: 'ar-SA',
                autoSave: true,
                autoSaveInterval: 5,
                maxRecentFiles: 20,
                thumbnailSize: 200,
                showHistogram: true,
                showMetadata: true,
                maxMemory: 4096,
                enableGPU: true,
                enableParallel: true,
                defaultFormat: 'JPEG',
                defaultQuality: 95,
                preserveMetadata: true,
                enableLua: true,
                enablePython: true
            };

            this.applyLoadedSettings();
            this.showNotification('تم إعادة تعيين الإعدادات / Settings reset', 'info');
        }
    }

    markAsModified() {
        // Visual indication that settings have been modified
        const saveBtn = document.getElementById('saveBtn');
        if (saveBtn && !saveBtn.classList.contains('modified')) {
            saveBtn.classList.add('modified');
            saveBtn.style.backgroundColor = 'var(--warning-color)';
        }
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        // Style the notification
        Object.assign(notification.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            padding: '12px 20px',
            borderRadius: '6px',
            color: 'white',
            fontWeight: '500',
            zIndex: '1000',
            opacity: '0',
            transform: 'translateY(-20px)',
            transition: 'all 0.3s ease'
        });

        // Set background color based on type
        const colors = {
            success: 'var(--success-color)',
            error: 'var(--error-color)',
            warning: 'var(--warning-color)',
            info: 'var(--info-color)'
        };
        notification.style.backgroundColor = colors[type] || colors.info;

        // Add to DOM
        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateY(0)';
        }, 10);

        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateY(-20px)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    notifyParent(event, data) {
        if (window.chrome && window.chrome.webview) {
            window.chrome.webview.postMessage({
                type: event,
                data: data
            });
        }
    }

    updateUI() {
        // Update any UI elements that depend on current state
        this.applyTheme();
        this.updateLanguage();
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.elashraffySettings = new ELashraffySettings();
});

// Handle messages from parent application
if (window.chrome && window.chrome.webview) {
    window.chrome.webview.addEventListener('message', (event) => {
        const message = event.data;
        
        switch (message.type) {
            case 'settingsLoaded':
                if (window.elashraffySettings) {
                    window.elashraffySettings.settings = message.data;
                    window.elashraffySettings.applyLoadedSettings();
                }
                break;
                
            case 'settingsSaved':
                if (window.elashraffySettings) {
                    window.elashraffySettings.showNotification(
                        'تم حفظ الإعدادات بنجاح / Settings saved successfully', 
                        'success'
                    );
                }
                break;
                
            case 'settingsError':
                if (window.elashraffySettings) {
                    window.elashraffySettings.showNotification(
                        'خطأ في حفظ الإعدادات / Error saving settings', 
                        'error'
                    );
                }
                break;
        }
    });
}
