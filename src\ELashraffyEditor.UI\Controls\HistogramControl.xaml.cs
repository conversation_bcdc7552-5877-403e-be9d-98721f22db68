using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Shapes;

namespace ELashraffyEditor.UI.Controls;

/// <summary>
/// عنصر تحكم الهيستوجرام
/// Histogram control
/// </summary>
public partial class HistogramControl : UserControl
{
    public static readonly DependencyProperty HistogramDataProperty =
        DependencyProperty.Register(nameof(HistogramData), typeof(int[][]), typeof(HistogramControl),
            new PropertyMetadata(null, OnHistogramDataChanged));

    public static readonly DependencyProperty ShowLuminanceProperty =
        DependencyProperty.Register(nameof(ShowLuminance), typeof(bool), typeof(HistogramControl),
            new PropertyMetadata(true, OnDisplayOptionsChanged));

    public static readonly DependencyProperty ShowRGBProperty =
        DependencyProperty.Register(nameof(ShowRGB), typeof(bool), typeof(HistogramControl),
            new PropertyMetadata(true, OnDisplayOptionsChanged));

    public int[][]? HistogramData
    {
        get => (int[][]?)GetValue(HistogramDataProperty);
        set => SetValue(HistogramDataProperty, value);
    }

    public bool ShowLuminance
    {
        get => (bool)GetValue(ShowLuminanceProperty);
        set => SetValue(ShowLuminanceProperty, value);
    }

    public bool ShowRGB
    {
        get => (bool)GetValue(ShowRGBProperty);
        set => SetValue(ShowRGBProperty, value);
    }

    public HistogramControl()
    {
        InitializeComponent();
    }

    private static void OnHistogramDataChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is HistogramControl control)
        {
            control.UpdateHistogram();
        }
    }

    private static void OnDisplayOptionsChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is HistogramControl control)
        {
            control.UpdateHistogram();
        }
    }

    private void HistogramCanvas_SizeChanged(object sender, SizeChangedEventArgs e)
    {
        UpdateHistogram();
    }

    private void UpdateHistogram()
    {
        if (HistogramData == null || HistogramCanvas.ActualWidth <= 0 || HistogramCanvas.ActualHeight <= 0)
            return;

        Dispatcher.BeginInvoke(() =>
        {
            HistogramCanvas.Children.Clear();
            DrawHistogram();
        });
    }

    private void DrawHistogram()
    {
        try
        {
            var canvasWidth = HistogramCanvas.ActualWidth;
            var canvasHeight = HistogramCanvas.ActualHeight;

            if (canvasWidth <= 0 || canvasHeight <= 0 || HistogramData == null)
                return;

            // حساب أعلى قيمة
            // Calculate maximum value
            var maxValue = CalculateMaxValue();
            if (maxValue == 0) return;

            // رسم الشبكة
            // Draw grid
            DrawGrid(canvasWidth, canvasHeight);

            // رسم قنوات RGB
            // Draw RGB channels
            if (ShowRGB)
            {
                DrawRGBChannels(canvasWidth, canvasHeight, maxValue);
            }

            // رسم قناة الإضاءة
            // Draw luminance channel
            if (ShowLuminance)
            {
                DrawLuminanceChannel(canvasWidth, canvasHeight, maxValue);
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error drawing histogram: {ex.Message}");
        }
    }

    private int CalculateMaxValue()
    {
        var maxValue = 0;
        
        if (HistogramData == null) return maxValue;

        // البحث في قنوات RGB
        // Search in RGB channels
        for (int channel = 0; channel < Math.Min(3, HistogramData.Length); channel++)
        {
            if (HistogramData[channel] != null)
            {
                for (int i = 0; i < Math.Min(256, HistogramData[channel].Length); i++)
                {
                    if (HistogramData[channel][i] > maxValue)
                        maxValue = HistogramData[channel][i];
                }
            }
        }

        return maxValue;
    }

    private void DrawGrid(double canvasWidth, double canvasHeight)
    {
        var gridBrush = new SolidColorBrush(Colors.Gray) { Opacity = 0.2 };

        // خطوط عمودية
        // Vertical lines
        for (int i = 1; i < 4; i++)
        {
            var x = (i / 4.0) * canvasWidth;
            var line = new Line
            {
                X1 = x, Y1 = 0,
                X2 = x, Y2 = canvasHeight,
                Stroke = gridBrush,
                StrokeThickness = 0.5
            };
            HistogramCanvas.Children.Add(line);
        }

        // خطوط أفقية
        // Horizontal lines
        for (int i = 1; i < 4; i++)
        {
            var y = (i / 4.0) * canvasHeight;
            var line = new Line
            {
                X1 = 0, Y1 = y,
                X2 = canvasWidth, Y2 = y,
                Stroke = gridBrush,
                StrokeThickness = 0.5
            };
            HistogramCanvas.Children.Add(line);
        }
    }

    private void DrawRGBChannels(double canvasWidth, double canvasHeight, int maxValue)
    {
        var channelColors = new[]
        {
            Colors.Red,
            Colors.Green,
            Colors.Blue
        };

        for (int channel = 0; channel < Math.Min(3, HistogramData!.Length); channel++)
        {
            if (HistogramData[channel] == null) continue;

            var pathGeometry = new PathGeometry();
            var pathFigure = new PathFigure { StartPoint = new Point(0, canvasHeight) };
            var polyLineSegment = new PolyLineSegment();

            // إنشاء نقاط المنحنى
            // Create curve points
            for (int i = 0; i < Math.Min(256, HistogramData[channel].Length); i++)
            {
                var x = (i / 255.0) * canvasWidth;
                var y = canvasHeight - ((HistogramData[channel][i] / (double)maxValue) * canvasHeight);
                polyLineSegment.Points.Add(new Point(x, y));
            }

            polyLineSegment.Points.Add(new Point(canvasWidth, canvasHeight));
            pathFigure.Segments.Add(polyLineSegment);
            pathGeometry.Figures.Add(pathFigure);

            var path = new Path
            {
                Data = pathGeometry,
                Fill = new SolidColorBrush(channelColors[channel]) { Opacity = 0.2 },
                Stroke = new SolidColorBrush(channelColors[channel]) { Opacity = 0.8 },
                StrokeThickness = 1
            };

            HistogramCanvas.Children.Add(path);
        }
    }

    private void DrawLuminanceChannel(double canvasWidth, double canvasHeight, int maxValue)
    {
        if (HistogramData == null || HistogramData.Length < 3) return;

        // حساب قناة الإضاءة من RGB
        // Calculate luminance channel from RGB
        var luminanceData = new int[256];
        
        for (int i = 0; i < 256; i++)
        {
            if (HistogramData[0] != null && HistogramData[1] != null && HistogramData[2] != null &&
                i < HistogramData[0].Length && i < HistogramData[1].Length && i < HistogramData[2].Length)
            {
                // استخدام معادلة الإضاءة القياسية
                // Use standard luminance formula
                luminanceData[i] = (int)(0.299 * HistogramData[0][i] + 
                                        0.587 * HistogramData[1][i] + 
                                        0.114 * HistogramData[2][i]);
            }
        }

        var pathGeometry = new PathGeometry();
        var pathFigure = new PathFigure { StartPoint = new Point(0, canvasHeight) };
        var polyLineSegment = new PolyLineSegment();

        for (int i = 0; i < 256; i++)
        {
            var x = (i / 255.0) * canvasWidth;
            var y = canvasHeight - ((luminanceData[i] / (double)maxValue) * canvasHeight);
            polyLineSegment.Points.Add(new Point(x, y));
        }

        polyLineSegment.Points.Add(new Point(canvasWidth, canvasHeight));
        pathFigure.Segments.Add(polyLineSegment);
        pathGeometry.Figures.Add(pathFigure);

        var path = new Path
        {
            Data = pathGeometry,
            Stroke = new SolidColorBrush(Colors.White) { Opacity = 0.9 },
            StrokeThickness = 1.5
        };

        HistogramCanvas.Children.Add(path);
    }
}
