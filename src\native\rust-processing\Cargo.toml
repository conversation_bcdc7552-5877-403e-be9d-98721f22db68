[package]
name = "elashrafy_image_processing"
version = "1.0.0"
edition = "2021"
authors = ["ELashrafy Team"]
description = "High-performance image processing library for ELashrafy Editor"
license = "MIT"

[lib]
name = "elashrafy_image_processing"
crate-type = ["cdylib", "staticlib"]

[dependencies]
image = "0.24"
imageproc = "0.23"
nalgebra = "0.32"
rayon = "1.7"
num-traits = "0.2"
thiserror = "1.0"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# FFI dependencies
libc = "0.2"

# Performance optimizations
[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"

[features]
default = ["simd"]
simd = []
gpu = []
