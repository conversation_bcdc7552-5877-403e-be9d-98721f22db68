# دليل التطوير - محرر الأشرافي
# Development Guide - ELashrafy Editor

## 📋 نظرة عامة / Overview

هذا الدليل يوضح كيفية إعداد بيئة التطوير والمساهمة في مشروع محرر الأشرافي.

This guide explains how to set up the development environment and contribute to the ELashrafy Editor project.

## 🛠️ متطلبات التطوير / Development Requirements

### الأساسية / Essential
- **Windows 10/11** (x64)
- **Visual Studio 2022** (Community أو أعلى)
- **.NET 6 SDK** أو أحدث
- **Git** لإدارة الإصدارات

### اختيارية / Optional
- **Rust** (للوحدات عالية الأداء)
- **Python 3.8+** (للذكاء الاصطناعي)
- **Node.js** (لتطوير واجهات الويب)

## 🚀 إعداد بيئة التطوير / Development Environment Setup

### 1. استنساخ المستودع / Clone Repository
```bash
git clone https://github.com/elashrafy/elashrafy-editor.git
cd elashrafy-editor
```

### 2. استعادة الحزم / Restore Packages
```bash
dotnet restore
```

### 3. بناء المشروع / Build Project
```bash
dotnet build --configuration Debug
```

### 4. تشغيل التطبيق / Run Application
```bash
dotnet run --project src/ELashraffyEditor.UI
```

## 🏗️ هيكل المشروع / Project Structure

```
ELashraffyEditor/
├── src/                           # الكود المصدري / Source code
│   ├── ELashraffyEditor.Core/     # المنطق الأساسي / Core logic
│   ├── ELashraffyEditor.UI/       # واجهة WPF / WPF interface
│   ├── ELashraffyEditor.Scripting/ # دعم البرمجة النصية / Scripting support
│   ├── ELashraffyEditor.WebUI/    # واجهات الويب / Web interfaces
│   └── native/                    # الوحدات الأصلية / Native modules
│       ├── rust-processing/       # معالجة Rust / Rust processing
│       └── python-ai/             # ذكاء اصطناعي Python / Python AI
├── tests/                         # الاختبارات / Tests
├── docs/                          # التوثيق / Documentation
└── dist/                          # ملفات التوزيع / Distribution files
```

## 🔧 تطوير المكونات / Component Development

### Core Library (C#)
```csharp
// مثال على إضافة خدمة جديدة
// Example of adding a new service
public interface INewService
{
    Task<bool> DoSomethingAsync();
}

public class NewService : INewService
{
    private readonly ILogger<NewService> _logger;
    
    public NewService(ILogger<NewService> logger)
    {
        _logger = logger;
    }
    
    public async Task<bool> DoSomethingAsync()
    {
        // تنفيذ الوظيفة
        // Implementation
        return true;
    }
}
```

### UI Development (WPF + MVVM)
```csharp
// مثال على ViewModel جديد
// Example of new ViewModel
public partial class NewViewModel : ObservableObject
{
    [ObservableProperty]
    private string _title = "New Feature";
    
    [RelayCommand]
    private async Task ExecuteActionAsync()
    {
        // تنفيذ الأمر
        // Command implementation
    }
}
```

### Lua Scripting
```lua
-- مثال على سكريبت Lua
-- Example Lua script
function processImage(image)
    print("معالجة الصورة / Processing image...")
    
    -- تطبيق التعديلات
    -- Apply adjustments
    image:AdjustBrightness(0.1)
    image:AdjustContrast(0.2)
    
    return true
end
```

### Python AI Integration
```python
# مثال على وحدة Python للذكاء الاصطناعي
# Example Python AI module
import cv2
import numpy as np

def enhance_image_ai(image_path):
    """تحسين الصورة بالذكاء الاصطناعي"""
    image = cv2.imread(image_path)
    
    # تطبيق خوارزميات الذكاء الاصطناعي
    # Apply AI algorithms
    enhanced = apply_ai_enhancement(image)
    
    return enhanced
```

### Rust Performance Modules
```rust
// مثال على وحدة Rust عالية الأداء
// Example high-performance Rust module
#[no_mangle]
pub extern "C" fn fast_image_processing(
    data: *mut u8,
    width: i32,
    height: i32
) -> bool {
    // معالجة سريعة للصورة
    // Fast image processing
    true
}
```

## 🧪 الاختبارات / Testing

### تشغيل جميع الاختبارات / Run All Tests
```bash
dotnet test
```

### اختبارات وحدة محددة / Specific Unit Tests
```bash
dotnet test tests/ELashraffyEditor.Tests/Core/ImageProcessingServiceTests.cs
```

### إضافة اختبار جديد / Adding New Test
```csharp
[Fact]
public async Task NewFeature_ValidInput_ShouldReturnExpectedResult()
{
    // Arrange
    var service = new NewService(_mockLogger.Object);
    
    // Act
    var result = await service.DoSomethingAsync();
    
    // Assert
    result.Should().BeTrue();
}
```

## 📦 البناء والنشر / Build and Deployment

### بناء للتطوير / Development Build
```bash
.\build.ps1 -Configuration Debug -Test
```

### بناء للإنتاج / Production Build
```bash
.\build.ps1 -Configuration Release -Publish -Package
```

### بناء مع Rust / Build with Rust
```bash
.\build.ps1 -BuildRust -Configuration Release
```

### تثبيت Python / Install Python Dependencies
```bash
.\build.ps1 -InstallPython
```

## 🎨 إرشادات التصميم / Design Guidelines

### Material Design
- استخدم ألوان النظام المحددة
- اتبع مبادئ Material Design
- حافظ على التناسق في الواجهات

### دعم اللغات / Language Support
- جميع النصوص يجب أن تكون قابلة للترجمة
- دعم RTL للعربية
- استخدم مفاتيح الترجمة بدلاً من النصوص المباشرة

```csharp
// صحيح / Correct
var message = _localizationService.GetLocalizedString("SaveSuccess");

// خطأ / Wrong
var message = "File saved successfully";
```

## 🔍 معايير الكود / Code Standards

### C# Conventions
- استخدم PascalCase للخصائص والطرق
- استخدم camelCase للمتغيرات المحلية
- أضف تعليقات XML للطرق العامة
- استخدم async/await للعمليات غير المتزامنة

### Naming Conventions
```csharp
// الواجهات / Interfaces
public interface IImageProcessor { }

// الخدمات / Services  
public class ImageProcessingService : IImageProcessor { }

// ViewModels
public class MainWindowViewModel : ObservableObject { }

// الأوامر / Commands
[RelayCommand]
private async Task SaveImageAsync() { }
```

## 🐛 تصحيح الأخطاء / Debugging

### Visual Studio
1. اضبط نقاط التوقف
2. استخدم نافذة Output للسجلات
3. فحص المتغيرات في نافذة Watch

### Logging
```csharp
_logger.LogInformation("Operation completed successfully");
_logger.LogWarning("Potential issue detected: {Issue}", issue);
_logger.LogError(ex, "Operation failed");
```

## 🤝 المساهمة / Contributing

### خطوات المساهمة / Contribution Steps
1. Fork المستودع
2. إنشاء فرع للميزة الجديدة
3. تطوير الميزة مع الاختبارات
4. إرسال Pull Request

### معايير Pull Request
- وصف واضح للتغييرات
- اختبارات شاملة
- توثيق محدث
- كود نظيف ومنظم

## 📚 موارد إضافية / Additional Resources

### التوثيق / Documentation
- [Material Design Guidelines](https://material.io/design)
- [WPF Documentation](https://docs.microsoft.com/en-us/dotnet/desktop/wpf/)
- [MVVM Pattern](https://docs.microsoft.com/en-us/dotnet/architecture/maui/mvvm)

### أدوات مفيدة / Useful Tools
- **Visual Studio Extensions**: XAML Styler, CodeMaid
- **Git Tools**: GitKraken, SourceTree
- **Design Tools**: Figma, Adobe XD

## 🆘 الحصول على المساعدة / Getting Help

### المشاكل الشائعة / Common Issues

#### مشكلة بناء المشروع / Build Issues
```bash
# تنظيف وإعادة البناء / Clean and rebuild
dotnet clean
dotnet restore
dotnet build
```

#### مشاكل WebView2 / WebView2 Issues
- تأكد من تثبيت WebView2 Runtime
- تحقق من إعدادات الأمان

#### مشاكل Python / Python Issues
```bash
# تحقق من إصدار Python / Check Python version
python --version

# تثبيت المتطلبات / Install requirements
pip install -r src/native/python-ai/requirements.txt
```

### طلب المساعدة / Asking for Help
- [GitHub Issues](https://github.com/elashrafy/elashrafy-editor/issues)
- [GitHub Discussions](https://github.com/elashrafy/elashrafy-editor/discussions)

---

**مع تحيات فريق محرر الأشرافي**  
**Best regards from ELashrafy Editor Team**
