using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;
using System.Windows.Input;
using ELashraffyEditor.Core.Models;
using ELashraffyEditor.UI.Services;
using Microsoft.Extensions.Logging;

namespace ELashraffyEditor.UI.ViewModels;

/// <summary>
/// ViewModel للنافذة الرئيسية
/// Main Window ViewModel
/// </summary>
public partial class MainWindowViewModel : ObservableObject
{
    private readonly ILogger<MainWindowViewModel> _logger;
    private readonly IThemeService _themeService;
    private readonly ILocalizationService _localizationService;
    private readonly ISettingsService _settingsService;

    [ObservableProperty]
    private string _title = "ELashrafy Editor - محرر الأشرافي";

    [ObservableProperty]
    private string _statusMessage = "جاهز / Ready";

    [ObservableProperty]
    private bool _isLoading;

    [ObservableProperty]
    private string _currentSection = "Library";

    [ObservableProperty]
    private ObservableCollection<PhotoItem> _photos = new();

    [ObservableProperty]
    private PhotoItem? _selectedPhoto;

    [ObservableProperty]
    private bool _isDarkTheme = true;

    [ObservableProperty]
    private string _currentLanguage = "ar-SA";

    [ObservableProperty]
    private double _memoryUsage;

    [ObservableProperty]
    private string _version = "1.0.0";

    public MainWindowViewModel(
        ILogger<MainWindowViewModel> logger,
        IThemeService themeService,
        ILocalizationService localizationService,
        ISettingsService settingsService)
    {
        _logger = logger;
        _themeService = themeService;
        _localizationService = localizationService;
        _settingsService = settingsService;

        // تحميل الإعدادات
        // Load settings
        LoadSettings();

        // إعداد الأوامر
        // Setup commands
        InitializeCommands();

        _logger.LogInformation("MainWindowViewModel initialized");
    }

    private void InitializeCommands()
    {
        // يمكن إضافة أوامر إضافية هنا
        // Additional commands can be added here
    }

    private void LoadSettings()
    {
        try
        {
            var settings = _settingsService.LoadSettings();
            
            IsDarkTheme = settings.TryGetValue("IsDarkTheme", out var darkTheme) && darkTheme is bool b ? b : true;
            CurrentLanguage = settings.TryGetValue("Language", out var language) && language is string s ? s : "ar-SA";
            
            // تطبيق الثيم
            // Apply theme
            _themeService.SetTheme(IsDarkTheme ? "Dark" : "Light");
            
            // تطبيق اللغة
            // Apply language
            _localizationService.SetLanguage(CurrentLanguage);
            
            _logger.LogInformation("Settings loaded successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load settings");
            StatusMessage = "فشل في تحميل الإعدادات / Failed to load settings";
        }
    }

    [RelayCommand]
    private async Task ImportPhotosAsync()
    {
        try
        {
            IsLoading = true;
            StatusMessage = "جاري استيراد الصور / Importing photos...";

            // هنا سيتم تنفيذ منطق استيراد الصور
            // Photo import logic will be implemented here
            await Task.Delay(1000); // محاكاة العملية / Simulate operation

            StatusMessage = "تم استيراد الصور بنجاح / Photos imported successfully";
            _logger.LogInformation("Photos imported successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to import photos");
            StatusMessage = "فشل في استيراد الصور / Failed to import photos";
        }
        finally
        {
            IsLoading = false;
        }
    }

    [RelayCommand]
    private void NavigateToSection(string section)
    {
        try
        {
            CurrentSection = section;
            StatusMessage = section switch
            {
                "Library" => "عرض المكتبة / Library view",
                "Edit" => "وضع التحرير / Edit mode",
                "Export" => "وضع التصدير / Export mode",
                "Settings" => "الإعدادات / Settings",
                _ => "قسم غير معروف / Unknown section"
            };

            _logger.LogInformation("Navigated to section: {Section}", section);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to navigate to section: {Section}", section);
            StatusMessage = "فشل في التنقل / Navigation failed";
        }
    }

    [RelayCommand]
    private async Task ToggleThemeAsync()
    {
        try
        {
            IsDarkTheme = !IsDarkTheme;
            _themeService.SetTheme(IsDarkTheme ? "Dark" : "Light");
            
            // حفظ الإعداد
            // Save setting
            var settings = _settingsService.LoadSettings();
            settings["IsDarkTheme"] = IsDarkTheme;
            _settingsService.SaveSettings(settings);

            StatusMessage = IsDarkTheme ? "تم التبديل للثيم المظلم / Switched to dark theme" 
                                       : "تم التبديل للثيم الفاتح / Switched to light theme";

            _logger.LogInformation("Theme toggled to: {Theme}", IsDarkTheme ? "Dark" : "Light");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to toggle theme");
            StatusMessage = "فشل في تغيير الثيم / Failed to change theme";
        }
    }

    [RelayCommand]
    private async Task ChangeLanguageAsync(string language)
    {
        try
        {
            CurrentLanguage = language;
            _localizationService.SetLanguage(language);
            
            // حفظ الإعداد
            // Save setting
            var settings = _settingsService.LoadSettings();
            settings["Language"] = language;
            _settingsService.SaveSettings(settings);

            StatusMessage = language == "ar-SA" ? "تم التبديل للعربية / Switched to Arabic"
                                                : "تم التبديل للإنجليزية / Switched to English";

            _logger.LogInformation("Language changed to: {Language}", language);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to change language");
            StatusMessage = "فشل في تغيير اللغة / Failed to change language";
        }
    }

    [RelayCommand]
    private void SelectPhoto(PhotoItem photo)
    {
        try
        {
            SelectedPhoto = photo;
            StatusMessage = $"تم اختيار الصورة: {photo.FileName} / Selected photo: {photo.FileName}";
            _logger.LogInformation("Photo selected: {FileName}", photo.FileName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to select photo");
            StatusMessage = "فشل في اختيار الصورة / Failed to select photo";
        }
    }

    [RelayCommand]
    private async Task RefreshAsync()
    {
        try
        {
            IsLoading = true;
            StatusMessage = "جاري التحديث / Refreshing...";

            // تحديث البيانات
            // Refresh data
            await Task.Delay(500); // محاكاة العملية / Simulate operation

            StatusMessage = "تم التحديث بنجاح / Refreshed successfully";
            _logger.LogInformation("Data refreshed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to refresh data");
            StatusMessage = "فشل في التحديث / Failed to refresh";
        }
        finally
        {
            IsLoading = false;
        }
    }

    public void UpdateMemoryUsage(double memoryMB)
    {
        MemoryUsage = memoryMB;
    }

    public void UpdateStatusMessage(string message)
    {
        StatusMessage = message;
    }

    public void AddPhoto(PhotoItem photo)
    {
        Photos.Add(photo);
        _logger.LogInformation("Photo added to collection: {FileName}", photo.FileName);
    }

    public void RemovePhoto(PhotoItem photo)
    {
        Photos.Remove(photo);
        if (SelectedPhoto == photo)
        {
            SelectedPhoto = null;
        }
        _logger.LogInformation("Photo removed from collection: {FileName}", photo.FileName);
    }

    public void ClearPhotos()
    {
        Photos.Clear();
        SelectedPhoto = null;
        _logger.LogInformation("Photo collection cleared");
    }
}
