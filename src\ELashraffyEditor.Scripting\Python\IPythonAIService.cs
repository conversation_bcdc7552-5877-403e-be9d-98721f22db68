using ELashraffyEditor.Core.Models;

namespace ELashraffyEditor.Scripting.Python;

/// <summary>
/// واجهة خدمة الذكاء الاصطناعي بـ Python
/// Python AI service interface
/// </summary>
public interface IPythonAIService
{
    /// <summary>
    /// تحسين تلقائي ذكي للصورة
    /// Smart auto enhancement for image
    /// </summary>
    Task<AIEnhancementResult> AutoEnhanceSmartAsync(string imagePath);

    /// <summary>
    /// كشف الوجوه في الصورة
    /// Detect faces in image
    /// </summary>
    Task<FaceDetectionResult> DetectFacesAsync(string imagePath);

    /// <summary>
    /// تحسين الصور الشخصية
    /// Enhance portrait photos
    /// </summary>
    Task<AIEnhancementResult> EnhancePortraitAsync(string imagePath);

    /// <summary>
    /// تصحيح الألوان التلقائي
    /// Automatic color correction
    /// </summary>
    Task<ColorCorrectionResult> AutoColorCorrectionAsync(string imagePath);

    /// <summary>
    /// تقليل الضوضاء بالذكاء الاصطناعي
    /// AI-powered noise reduction
    /// </summary>
    Task<NoiseReductionResult> ReduceNoiseAIAsync(string imagePath, float strength = 0.5f);

    /// <summary>
    /// تحليل جودة الصورة
    /// Analyze image quality
    /// </summary>
    Task<ImageQualityAnalysis> AnalyzeImageQualityAsync(string imagePath);

    /// <summary>
    /// اقتراح تحسينات للصورة
    /// Suggest image improvements
    /// </summary>
    Task<ImprovementSuggestions> SuggestImprovementsAsync(string imagePath);

    /// <summary>
    /// تصنيف نوع الصورة
    /// Classify image type
    /// </summary>
    Task<ImageClassificationResult> ClassifyImageAsync(string imagePath);

    /// <summary>
    /// التحقق من توفر Python والمكتبات المطلوبة
    /// Check Python and required libraries availability
    /// </summary>
    Task<bool> CheckPythonEnvironmentAsync();

    /// <summary>
    /// تثبيت المكتبات المطلوبة
    /// Install required libraries
    /// </summary>
    Task<bool> InstallRequiredLibrariesAsync();

    /// <summary>
    /// الحصول على معلومات البيئة
    /// Get environment information
    /// </summary>
    Task<PythonEnvironmentInfo> GetEnvironmentInfoAsync();
}

/// <summary>
/// نتيجة التحسين بالذكاء الاصطناعي
/// AI enhancement result
/// </summary>
public class AIEnhancementResult
{
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public ImageAdjustment? Adjustments { get; set; }
    public Dictionary<string, object>? Analysis { get; set; }
    public string? ImageType { get; set; }
    public float ConfidenceScore { get; set; }
}

/// <summary>
/// نتيجة كشف الوجوه
/// Face detection result
/// </summary>
public class FaceDetectionResult
{
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public List<DetectedFace> Faces { get; set; } = new();
    public int FaceCount { get; set; }
}

/// <summary>
/// وجه مكتشف
/// Detected face
/// </summary>
public class DetectedFace
{
    public int X { get; set; }
    public int Y { get; set; }
    public int Width { get; set; }
    public int Height { get; set; }
    public float Confidence { get; set; }
    public Dictionary<string, object>? Features { get; set; }
}

/// <summary>
/// نتيجة تصحيح الألوان
/// Color correction result
/// </summary>
public class ColorCorrectionResult
{
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public float TemperatureShift { get; set; }
    public float TintShift { get; set; }
    public bool WhiteBalanceCorrection { get; set; }
    public string? ColorCast { get; set; }
    public Dictionary<string, object>? Analysis { get; set; }
}

/// <summary>
/// نتيجة تقليل الضوضاء
/// Noise reduction result
/// </summary>
public class NoiseReductionResult
{
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public string? OutputPath { get; set; }
    public string? NoiseType { get; set; }
    public float NoiseLevel { get; set; }
    public string? NoiseSeverity { get; set; }
    public float StrengthUsed { get; set; }
}

/// <summary>
/// تحليل جودة الصورة
/// Image quality analysis
/// </summary>
public class ImageQualityAnalysis
{
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public float OverallQuality { get; set; }
    public float Sharpness { get; set; }
    public float Brightness { get; set; }
    public float Contrast { get; set; }
    public float ColorBalance { get; set; }
    public float NoiseLevel { get; set; }
    public List<string> Issues { get; set; } = new();
    public Dictionary<string, float> Metrics { get; set; } = new();
}

/// <summary>
/// اقتراحات التحسين
/// Improvement suggestions
/// </summary>
public class ImprovementSuggestions
{
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public List<Suggestion> Suggestions { get; set; } = new();
    public ImageAdjustment? RecommendedAdjustments { get; set; }
    public int Priority { get; set; }
}

/// <summary>
/// اقتراح تحسين
/// Improvement suggestion
/// </summary>
public class Suggestion
{
    public string Type { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string DescriptionArabic { get; set; } = string.Empty;
    public int Priority { get; set; }
    public float Impact { get; set; }
    public Dictionary<string, object>? Parameters { get; set; }
}

/// <summary>
/// نتيجة تصنيف الصورة
/// Image classification result
/// </summary>
public class ImageClassificationResult
{
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public string ImageType { get; set; } = string.Empty;
    public float Confidence { get; set; }
    public List<Classification> Classifications { get; set; } = new();
    public Dictionary<string, object>? Features { get; set; }
}

/// <summary>
/// تصنيف
/// Classification
/// </summary>
public class Classification
{
    public string Category { get; set; } = string.Empty;
    public string Label { get; set; } = string.Empty;
    public float Confidence { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
}

/// <summary>
/// معلومات بيئة Python
/// Python environment information
/// </summary>
public class PythonEnvironmentInfo
{
    public bool PythonAvailable { get; set; }
    public string? PythonVersion { get; set; }
    public string? PythonPath { get; set; }
    public List<string> InstalledPackages { get; set; } = new();
    public List<string> MissingPackages { get; set; } = new();
    public bool AllRequirementsMet { get; set; }
    public Dictionary<string, string> PackageVersions { get; set; } = new();
}
