using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using ELashraffyEditor.UI.Services;
using Microsoft.Extensions.Logging;
using Microsoft.Web.WebView2.Wpf;
using System.Text.Json;
using System.IO;

namespace ELashraffyEditor.UI.ViewModels;

/// <summary>
/// ViewModel لصفحة الإعدادات
/// Settings page view model
/// </summary>
public partial class SettingsViewModel : ObservableObject
{
    private readonly ILogger<SettingsViewModel> _logger;
    private readonly ISettingsService _settingsService;
    private readonly IThemeService _themeService;
    private readonly ILocalizationService _localizationService;
    private readonly IDialogService _dialogService;
    private WebView2? _webView;

    [ObservableProperty]
    private bool _isWebViewReady;

    [ObservableProperty]
    private string _webViewSource = "about:blank";

    [ObservableProperty]
    private bool _isDarkTheme = true;

    [ObservableProperty]
    private string _currentLanguage = "ar-SA";

    [ObservableProperty]
    private bool _autoSaveEnabled = true;

    [ObservableProperty]
    private int _autoSaveInterval = 5;

    [ObservableProperty]
    private int _maxRecentFiles = 20;

    [ObservableProperty]
    private int _thumbnailSize = 200;

    [ObservableProperty]
    private bool _showHistogram = true;

    [ObservableProperty]
    private bool _showMetadata = true;

    [ObservableProperty]
    private int _maxMemoryUsage = 4096;

    [ObservableProperty]
    private bool _enableGPUAcceleration = true;

    [ObservableProperty]
    private bool _enableParallelProcessing = true;

    [ObservableProperty]
    private string _defaultExportFormat = "JPEG";

    [ObservableProperty]
    private int _defaultExportQuality = 95;

    [ObservableProperty]
    private bool _preserveMetadata = true;

    [ObservableProperty]
    private bool _enableLuaScripting = true;

    [ObservableProperty]
    private bool _enablePythonScripting = true;

    public SettingsViewModel(
        ILogger<SettingsViewModel> logger,
        ISettingsService settingsService,
        IThemeService themeService,
        ILocalizationService localizationService,
        IDialogService dialogService)
    {
        _logger = logger;
        _settingsService = settingsService;
        _themeService = themeService;
        _localizationService = localizationService;
        _dialogService = dialogService;

        LoadSettings();
        _logger.LogInformation("SettingsViewModel initialized");
    }

    public void SetWebView(WebView2 webView)
    {
        _webView = webView;
        SetupWebView();
    }

    private async void SetupWebView()
    {
        if (_webView == null) return;

        try
        {
            // انتظار تهيئة WebView2
            // Wait for WebView2 initialization
            await _webView.EnsureCoreWebView2Async();

            // إعداد معالج الرسائل
            // Setup message handler
            _webView.CoreWebView2.WebMessageReceived += OnWebMessageReceived;

            // تحميل صفحة الإعدادات
            // Load settings page
            var htmlPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "src", "ELashraffyEditor.WebUI", "index.html");
            if (File.Exists(htmlPath))
            {
                WebViewSource = $"file:///{htmlPath.Replace('\\', '/')}";
                _webView.Source = new Uri(WebViewSource);
            }
            else
            {
                // استخدام HTML مدمج كبديل
                // Use embedded HTML as fallback
                LoadEmbeddedHtmlAsync();
            }

            IsWebViewReady = true;
            _logger.LogInformation("WebView2 initialized successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize WebView2");
            _dialogService.ShowError($"فشل في تهيئة واجهة الإعدادات: {ex.Message}\nFailed to initialize settings interface: {ex.Message}");
        }
    }

    private void OnWebMessageReceived(object? sender, Microsoft.Web.WebView2.Core.CoreWebView2WebMessageReceivedEventArgs e)
    {
        try
        {
            var messageJson = e.TryGetWebMessageAsString();
            if (string.IsNullOrEmpty(messageJson)) return;

            var message = JsonSerializer.Deserialize<WebMessage>(messageJson);
            if (message == null) return;

            HandleWebMessageAsync(message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to handle web message");
        }
    }

    private void HandleWebMessageAsync(WebMessage message)
    {
        switch (message.Type)
        {
            case "getSettings":
                SendSettingsToWebViewAsync();
                break;

            case "saveSettings":
                if (message.Data != null)
                {
                    SaveSettingsFromWebViewAsync(message.Data);
                }
                break;

            case "themeChanged":
                if (message.Data?.TryGetValue("theme", out var themeValue) == true)
                {
                    var theme = themeValue.ToString();
                    if (!string.IsNullOrEmpty(theme))
                    {
                        IsDarkTheme = theme == "dark";
                        _themeService.SetTheme(theme);
                    }
                }
                break;

            case "languageChanged":
                if (message.Data?.TryGetValue("language", out var languageValue) == true)
                {
                    var language = languageValue.ToString();
                    if (!string.IsNullOrEmpty(language))
                    {
                        CurrentLanguage = language;
                        _localizationService.SetLanguage(language);
                    }
                }
                break;

            default:
                _logger.LogWarning("Unknown web message type: {Type}", message.Type);
                break;
        }
    }

    private void SendSettingsToWebViewAsync()
    {
        if (_webView?.CoreWebView2 == null) return;

        try
        {
            var settings = CollectCurrentSettings();
            var response = new WebMessage
            {
                Type = "settingsLoaded",
                Data = settings
            };

            var json = JsonSerializer.Serialize(response);
            _webView.CoreWebView2.PostWebMessageAsJson(json);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send settings to WebView");
        }
    }

    private void SaveSettingsFromWebViewAsync(Dictionary<string, object> webSettings)
    {
        try
        {
            // تحديث الخصائص من WebView
            // Update properties from WebView
            UpdatePropertiesFromWebSettings(webSettings);

            // حفظ الإعدادات
            // Save settings
            SaveSettings();

            // إرسال تأكيد النجاح
            // Send success confirmation
            var response = new WebMessage { Type = "settingsSaved" };
            var json = JsonSerializer.Serialize(response);
            _webView!.CoreWebView2.PostWebMessageAsJson(json);

            _logger.LogInformation("Settings saved from WebView");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save settings from WebView");
            
            // إرسال رسالة خطأ
            // Send error message
            var response = new WebMessage { Type = "settingsError" };
            var json = JsonSerializer.Serialize(response);
            _webView!.CoreWebView2.PostWebMessageAsJson(json);
        }
    }

    private void UpdatePropertiesFromWebSettings(Dictionary<string, object> webSettings)
    {
        if (webSettings.TryGetValue("theme", out var theme))
            IsDarkTheme = theme.ToString() == "dark";

        if (webSettings.TryGetValue("language", out var language))
            CurrentLanguage = language.ToString() ?? "ar-SA";

        if (webSettings.TryGetValue("autoSave", out var autoSave) && bool.TryParse(autoSave.ToString(), out var autoSaveBool))
            AutoSaveEnabled = autoSaveBool;

        if (webSettings.TryGetValue("autoSaveInterval", out var interval) && int.TryParse(interval.ToString(), out var intervalInt))
            AutoSaveInterval = intervalInt;

        if (webSettings.TryGetValue("maxRecentFiles", out var maxRecent) && int.TryParse(maxRecent.ToString(), out var maxRecentInt))
            MaxRecentFiles = maxRecentInt;

        if (webSettings.TryGetValue("thumbnailSize", out var thumbSize) && int.TryParse(thumbSize.ToString(), out var thumbSizeInt))
            ThumbnailSize = thumbSizeInt;

        if (webSettings.TryGetValue("showHistogram", out var showHist) && bool.TryParse(showHist.ToString(), out var showHistBool))
            ShowHistogram = showHistBool;

        if (webSettings.TryGetValue("showMetadata", out var showMeta) && bool.TryParse(showMeta.ToString(), out var showMetaBool))
            ShowMetadata = showMetaBool;

        if (webSettings.TryGetValue("maxMemory", out var maxMem) && int.TryParse(maxMem.ToString(), out var maxMemInt))
            MaxMemoryUsage = maxMemInt;

        if (webSettings.TryGetValue("enableGPU", out var enableGpu) && bool.TryParse(enableGpu.ToString(), out var enableGpuBool))
            EnableGPUAcceleration = enableGpuBool;

        if (webSettings.TryGetValue("enableParallel", out var enablePar) && bool.TryParse(enablePar.ToString(), out var enableParBool))
            EnableParallelProcessing = enableParBool;

        if (webSettings.TryGetValue("defaultFormat", out var format))
            DefaultExportFormat = format.ToString() ?? "JPEG";

        if (webSettings.TryGetValue("defaultQuality", out var quality) && int.TryParse(quality.ToString(), out var qualityInt))
            DefaultExportQuality = qualityInt;

        if (webSettings.TryGetValue("preserveMetadata", out var preserve) && bool.TryParse(preserve.ToString(), out var preserveBool))
            PreserveMetadata = preserveBool;

        if (webSettings.TryGetValue("enableLua", out var enableLua) && bool.TryParse(enableLua.ToString(), out var enableLuaBool))
            EnableLuaScripting = enableLuaBool;

        if (webSettings.TryGetValue("enablePython", out var enablePython) && bool.TryParse(enablePython.ToString(), out var enablePythonBool))
            EnablePythonScripting = enablePythonBool;
    }

    private Dictionary<string, object> CollectCurrentSettings()
    {
        return new Dictionary<string, object>
        {
            { "theme", IsDarkTheme ? "dark" : "light" },
            { "language", CurrentLanguage },
            { "autoSave", AutoSaveEnabled },
            { "autoSaveInterval", AutoSaveInterval },
            { "maxRecentFiles", MaxRecentFiles },
            { "thumbnailSize", ThumbnailSize },
            { "showHistogram", ShowHistogram },
            { "showMetadata", ShowMetadata },
            { "maxMemory", MaxMemoryUsage },
            { "enableGPU", EnableGPUAcceleration },
            { "enableParallel", EnableParallelProcessing },
            { "defaultFormat", DefaultExportFormat },
            { "defaultQuality", DefaultExportQuality },
            { "preserveMetadata", PreserveMetadata },
            { "enableLua", EnableLuaScripting },
            { "enablePython", EnablePythonScripting }
        };
    }

    private void LoadSettings()
    {
        try
        {
            var settings = _settingsService.LoadSettings();

            IsDarkTheme = _settingsService.GetSetting("IsDarkTheme", true);
            CurrentLanguage = _settingsService.GetSetting("Language", "ar-SA");
            AutoSaveEnabled = _settingsService.GetSetting("AutoSaveEnabled", true);
            AutoSaveInterval = _settingsService.GetSetting("AutoSaveInterval", 5);
            MaxRecentFiles = _settingsService.GetSetting("MaxRecentFiles", 20);
            ThumbnailSize = _settingsService.GetSetting("ThumbnailSize", 200);
            ShowHistogram = _settingsService.GetSetting("ShowHistogram", true);
            ShowMetadata = _settingsService.GetSetting("ShowMetadata", true);
            MaxMemoryUsage = _settingsService.GetSetting("MaxMemoryUsage", 4096);
            EnableGPUAcceleration = _settingsService.GetSetting("EnableGPUAcceleration", true);
            EnableParallelProcessing = _settingsService.GetSetting("EnableParallelProcessing", true);
            DefaultExportFormat = _settingsService.GetSetting("DefaultExportFormat", "JPEG");
            DefaultExportQuality = _settingsService.GetSetting("DefaultExportQuality", 95);
            PreserveMetadata = _settingsService.GetSetting("PreserveMetadata", true);
            EnableLuaScripting = _settingsService.GetSetting("EnableLuaScripting", true);
            EnablePythonScripting = _settingsService.GetSetting("EnablePythonScripting", true);

            _logger.LogInformation("Settings loaded successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load settings");
        }
    }

    [RelayCommand]
    private void SaveSettings()
    {
        try
        {
            _settingsService.SetSetting("IsDarkTheme", IsDarkTheme);
            _settingsService.SetSetting("Language", CurrentLanguage);
            _settingsService.SetSetting("AutoSaveEnabled", AutoSaveEnabled);
            _settingsService.SetSetting("AutoSaveInterval", AutoSaveInterval);
            _settingsService.SetSetting("MaxRecentFiles", MaxRecentFiles);
            _settingsService.SetSetting("ThumbnailSize", ThumbnailSize);
            _settingsService.SetSetting("ShowHistogram", ShowHistogram);
            _settingsService.SetSetting("ShowMetadata", ShowMetadata);
            _settingsService.SetSetting("MaxMemoryUsage", MaxMemoryUsage);
            _settingsService.SetSetting("EnableGPUAcceleration", EnableGPUAcceleration);
            _settingsService.SetSetting("EnableParallelProcessing", EnableParallelProcessing);
            _settingsService.SetSetting("DefaultExportFormat", DefaultExportFormat);
            _settingsService.SetSetting("DefaultExportQuality", DefaultExportQuality);
            _settingsService.SetSetting("PreserveMetadata", PreserveMetadata);
            _settingsService.SetSetting("EnableLuaScripting", EnableLuaScripting);
            _settingsService.SetSetting("EnablePythonScripting", EnablePythonScripting);

            _settingsService.SaveSettings();

            // تطبيق الإعدادات
            // Apply settings
            _themeService.SetTheme(IsDarkTheme ? "Dark" : "Light");
            _localizationService.SetLanguage(CurrentLanguage);

            _logger.LogInformation("Settings saved successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save settings");
            _dialogService.ShowError($"فشل في حفظ الإعدادات: {ex.Message}\nFailed to save settings: {ex.Message}");
        }
    }

    [RelayCommand]
    private void ResetToDefaults()
    {
        if (_dialogService.ShowConfirmation("هل تريد إعادة تعيين جميع الإعدادات للقيم الافتراضية؟\nDo you want to reset all settings to default values?"))
        {
            _settingsService.ResetToDefaults();
            LoadSettings();
            
            _dialogService.ShowInformation("تم إعادة تعيين الإعدادات بنجاح\nSettings reset successfully");
        }
    }

    private void LoadEmbeddedHtmlAsync()
    {
        if (_webView?.CoreWebView2 == null) return;

        var html = @"
<!DOCTYPE html>
<html>
<head>
    <title>ELashrafy Editor Settings</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #1e1e1e; color: white; }
        .container { max-width: 800px; margin: 0 auto; }
        h1 { color: #673ab7; }
        .setting-group { margin: 20px 0; padding: 15px; background: #2d2d30; border-radius: 8px; }
        .setting-item { margin: 10px 0; }
        label { display: block; margin-bottom: 5px; }
        input, select { padding: 8px; border: 1px solid #404040; background: #3e3e42; color: white; border-radius: 4px; }
        button { padding: 10px 20px; background: #673ab7; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #7c4dff; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>إعدادات محرر الأشرافي / ELashrafy Editor Settings</h1>
        <div class='setting-group'>
            <h3>الإعدادات الأساسية / Basic Settings</h3>
            <div class='setting-item'>
                <label>اللغة / Language:</label>
                <select id='language'>
                    <option value='ar-SA'>العربية</option>
                    <option value='en-US'>English</option>
                </select>
            </div>
            <div class='setting-item'>
                <label>الثيم / Theme:</label>
                <select id='theme'>
                    <option value='dark'>مظلم / Dark</option>
                    <option value='light'>فاتح / Light</option>
                </select>
            </div>
        </div>
        <button onclick='saveSettings()'>حفظ / Save</button>
    </div>
    <script>
        function saveSettings() {
            const settings = {
                language: document.getElementById('language').value,
                theme: document.getElementById('theme').value
            };
            window.chrome.webview.postMessage({type: 'saveSettings', data: settings});
        }
    </script>
</body>
</html>";

        _webView.CoreWebView2.NavigateToString(html);
    }
}

/// <summary>
/// نموذج رسالة WebView
/// WebView message model
/// </summary>
public class WebMessage
{
    public string Type { get; set; } = string.Empty;
    public Dictionary<string, object>? Data { get; set; }
}
