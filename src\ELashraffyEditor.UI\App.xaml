<Application x:Class="ELashraffyEditor.UI.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             StartupUri="Views/MainWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Material Design -->
                <materialDesign:BundledTheme BaseTheme="Dark" PrimaryColor="DeepPurple" SecondaryColor="Lime" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
                
                <!-- Custom Styles -->
                <ResourceDictionary Source="Resources/Themes/DarkTheme.xaml" />
                <ResourceDictionary Source="Resources/Themes/CustomStyles.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- Global Colors -->
            <SolidColorBrush x:Key="PrimaryBackgroundBrush" Color="#FF1E1E1E"/>
            <SolidColorBrush x:Key="SecondaryBackgroundBrush" Color="#FF2D2D30"/>
            <SolidColorBrush x:Key="TertiaryBackgroundBrush" Color="#FF3E3E42"/>
            <SolidColorBrush x:Key="PrimaryTextBrush" Color="#FFFFFFFF"/>
            <SolidColorBrush x:Key="SecondaryTextBrush" Color="#FFB0B0B0"/>
            <SolidColorBrush x:Key="AccentBrush" Color="#FF673AB7"/>
            <SolidColorBrush x:Key="AccentHoverBrush" Color="#FF7C4DFF"/>
            <SolidColorBrush x:Key="BorderBrush" Color="#FF404040"/>
            <SolidColorBrush x:Key="HighlightBrush" Color="#FF2196F3"/>

            <!-- Global Font Families -->
            <FontFamily x:Key="PrimaryFontFamily">Segoe UI</FontFamily>
            <FontFamily x:Key="ArabicFontFamily">Tahoma</FontFamily>

            <!-- Global Font Sizes -->
            <sys:Double x:Key="HeaderFontSize" xmlns:sys="clr-namespace:System;assembly=mscorlib">24</sys:Double>
            <sys:Double x:Key="SubHeaderFontSize" xmlns:sys="clr-namespace:System;assembly=mscorlib">18</sys:Double>
            <sys:Double x:Key="BodyFontSize" xmlns:sys="clr-namespace:System;assembly=mscorlib">14</sys:Double>
            <sys:Double x:Key="CaptionFontSize" xmlns:sys="clr-namespace:System;assembly=mscorlib">12</sys:Double>

            <!-- Global Margins and Paddings -->
            <Thickness x:Key="DefaultMargin">8</Thickness>
            <Thickness x:Key="DefaultPadding">12</Thickness>
            <Thickness x:Key="SmallMargin">4</Thickness>
            <Thickness x:Key="SmallPadding">6</Thickness>
            <Thickness x:Key="LargeMargin">16</Thickness>
            <Thickness x:Key="LargePadding">20</Thickness>

            <!-- Global Corner Radius -->
            <CornerRadius x:Key="DefaultCornerRadius">4</CornerRadius>
            <CornerRadius x:Key="LargeCornerRadius">8</CornerRadius>

            <!-- Animation Durations -->
            <Duration x:Key="FastAnimation">0:0:0.2</Duration>
            <Duration x:Key="NormalAnimation">0:0:0.3</Duration>
            <Duration x:Key="SlowAnimation">0:0:0.5</Duration>

        </ResourceDictionary>
    </Application.Resources>
</Application>
