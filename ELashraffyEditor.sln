Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ELashraffyEditor.Core", "src\ELashraffyEditor.Core\ELashraffyEditor.Core.csproj", "{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ELashraffyEditor.UI", "src\ELashraffyEditor.UI\ELashraffyEditor.UI.csproj", "{B2C3D4E5-F6G7-8901-BCDE-F23456789012}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ELashraffyEditor.Scripting", "src\ELashraffyEditor.Scripting\ELashraffyEditor.Scripting.csproj", "{C3D4E5F6-G7H8-9012-CDEF-************}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{D4E5F6G7-H8I9-0123-DEF4-56789012345A}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{E5F6G7H8-I9J0-1234-EF56-789012345678}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "docs", "docs", "{F6G7H8I9-J0K1-2345-F678-90123456789A}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "native", "native", "{G7H8I9J0-K1L2-3456-789A-BCDEF0123456}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|x64.ActiveCfg = Debug|x64
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|x64.Build.0 = Debug|x64
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|x86.ActiveCfg = Debug|x86
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|x86.Build.0 = Debug|x86
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|Any CPU.Build.0 = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|x64.ActiveCfg = Release|x64
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|x64.Build.0 = Release|x64
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|x86.ActiveCfg = Release|x86
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|x86.Build.0 = Release|x86
		{B2C3D4E5-F6G7-8901-BCDE-F23456789012}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B2C3D4E5-F6G7-8901-BCDE-F23456789012}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B2C3D4E5-F6G7-8901-BCDE-F23456789012}.Debug|x64.ActiveCfg = Debug|x64
		{B2C3D4E5-F6G7-8901-BCDE-F23456789012}.Debug|x64.Build.0 = Debug|x64
		{B2C3D4E5-F6G7-8901-BCDE-F23456789012}.Debug|x86.ActiveCfg = Debug|x86
		{B2C3D4E5-F6G7-8901-BCDE-F23456789012}.Debug|x86.Build.0 = Debug|x86
		{B2C3D4E5-F6G7-8901-BCDE-F23456789012}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B2C3D4E5-F6G7-8901-BCDE-F23456789012}.Release|Any CPU.Build.0 = Release|Any CPU
		{B2C3D4E5-F6G7-8901-BCDE-F23456789012}.Release|x64.ActiveCfg = Release|x64
		{B2C3D4E5-F6G7-8901-BCDE-F23456789012}.Release|x64.Build.0 = Release|x64
		{B2C3D4E5-F6G7-8901-BCDE-F23456789012}.Release|x86.ActiveCfg = Release|x86
		{B2C3D4E5-F6G7-8901-BCDE-F23456789012}.Release|x86.Build.0 = Release|x86
		{C3D4E5F6-G7H8-9012-CDEF-************}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C3D4E5F6-G7H8-9012-CDEF-************}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C3D4E5F6-G7H8-9012-CDEF-************}.Debug|x64.ActiveCfg = Debug|x64
		{C3D4E5F6-G7H8-9012-CDEF-************}.Debug|x64.Build.0 = Debug|x64
		{C3D4E5F6-G7H8-9012-CDEF-************}.Debug|x86.ActiveCfg = Debug|x86
		{C3D4E5F6-G7H8-9012-CDEF-************}.Debug|x86.Build.0 = Debug|x86
		{C3D4E5F6-G7H8-9012-CDEF-************}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C3D4E5F6-G7H8-9012-CDEF-************}.Release|Any CPU.Build.0 = Release|Any CPU
		{C3D4E5F6-G7H8-9012-CDEF-************}.Release|x64.ActiveCfg = Release|x64
		{C3D4E5F6-G7H8-9012-CDEF-************}.Release|x64.Build.0 = Release|x64
		{C3D4E5F6-G7H8-9012-CDEF-************}.Release|x86.ActiveCfg = Release|x86
		{C3D4E5F6-G7H8-9012-CDEF-************}.Release|x86.Build.0 = Release|x86
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890} = {D4E5F6G7-H8I9-0123-DEF4-56789012345A}
		{B2C3D4E5-F6G7-8901-BCDE-F23456789012} = {D4E5F6G7-H8I9-0123-DEF4-56789012345A}
		{C3D4E5F6-G7H8-9012-CDEF-************} = {D4E5F6G7-H8I9-0123-DEF4-56789012345A}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {12345678-1234-5678-9ABC-DEF012345678}
	EndGlobalSection
EndGlobal
