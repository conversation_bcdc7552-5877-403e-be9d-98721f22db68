-- سكريبت تحسين الصور الشخصية
-- Portrait Enhancement Script
-- محرر الأشرافي / ELashrafy Editor

print("بدء تحسين الصورة الشخصية / Starting portrait enhancement...")

-- التحقق من وجود الصورة
-- Check if image exists
if not image then
    print("خطأ: لا توجد صورة للمعالجة / Error: No image to process")
    return false
end

-- طباعة معلومات الصورة
-- Print image information
print("أبعاد الصورة / Image dimensions: " .. image.Width .. "x" .. image.Height)

-- تحسين الإضاءة للوجه
-- Enhance lighting for face
function enhanceFaceLighting()
    print("تحسين إضاءة الوجه / Enhancing face lighting...")
    
    -- رفع الظلال قليلاً لإظهار تفاصيل الوجه
    -- Lift shadows slightly to show face details
    image:AdjustBrightness(0.1)
    
    -- تحسين التباين للحصول على عمق أكبر
    -- Improve contrast for more depth
    image:AdjustContrast(0.15)
    
    -- تقليل التعرض الزائد إذا وجد
    -- Reduce overexposure if present
    image:AdjustExposure(-0.2)
    
    return true
end

-- تحسين لون البشرة
-- Enhance skin tone
function enhanceSkinTone()
    print("تحسين لون البشرة / Enhancing skin tone...")
    
    -- تحسين التشبع بحذر للحصول على لون طبيعي
    -- Carefully enhance saturation for natural color
    image:AdjustSaturation(0.1)
    
    return true
end

-- تطبيق تحسينات ناعمة
-- Apply gentle enhancements
function applySoftEnhancements()
    print("تطبيق تحسينات ناعمة / Applying soft enhancements...")
    
    -- تحسين طفيف للوضوح
    -- Slight clarity enhancement
    if adjustment then
        adjustment.Clarity = 10
        adjustment.Vibrance = 15
        adjustment.Shadows = 20
        adjustment.Highlights = -10
    end
    
    return true
end

-- دالة التحسين الرئيسية
-- Main enhancement function
function enhancePortrait()
    print("تشغيل تحسين الصورة الشخصية / Running portrait enhancement...")
    
    local success = true
    
    -- تطبيق التحسينات خطوة بخطوة
    -- Apply enhancements step by step
    success = success and enhanceFaceLighting()
    success = success and enhanceSkinTone()
    success = success and applySoftEnhancements()
    
    if success then
        print("تم تحسين الصورة بنجاح / Portrait enhanced successfully!")
        log("Portrait enhancement completed for image " .. image.Width .. "x" .. image.Height)
    else
        print("فشل في تحسين الصورة / Failed to enhance portrait")
    end
    
    return success
end

-- تشغيل السكريبت
-- Run the script
local result = enhancePortrait()

-- إرجاع النتيجة
-- Return result
return result
