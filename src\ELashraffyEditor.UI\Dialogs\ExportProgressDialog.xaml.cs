using System.ComponentModel;
using System.Windows;
using System.Windows.Controls;

namespace ELashraffyEditor.UI.Dialogs;

/// <summary>
/// حوار تقدم التصدير
/// Export progress dialog
/// </summary>
public partial class ExportProgressDialog : Window
{
    private bool _isPaused = false;
    private bool _isCancelled = false;
    private DateTime _startTime;
    private int _totalFiles;
    private int _completedFiles;
    private int _failedFiles;

    public bool IsCancelled => _isCancelled;
    public bool IsPaused => _isPaused;

    public ExportProgressDialog()
    {
        InitializeComponent();
        _startTime = DateTime.Now;
    }

    public void Initialize(int totalFiles)
    {
        _totalFiles = totalFiles;
        _completedFiles = 0;
        _failedFiles = 0;
        
        UpdateUI();
    }

    public void UpdateProgress(int progress, string currentFile)
    {
        Dispatcher.Invoke(() =>
        {
            OverallProgressBar.Value = progress;
            OverallProgressText.Text = $"{progress}%";
            CurrentFileText.Text = currentFile;
            
            UpdateStatistics();
            UpdateTimeRemaining();
        });
    }

    public void UpdateFileProgress(int fileProgress)
    {
        Dispatcher.Invoke(() =>
        {
            CurrentFileProgressBar.IsIndeterminate = false;
            CurrentFileProgressBar.Value = fileProgress;
        });
    }

    public void FileCompleted(bool success, string fileName)
    {
        if (success)
        {
            _completedFiles++;
            AddLogMessage($"✓ تم تصدير: {fileName} / Exported: {fileName}");
        }
        else
        {
            _failedFiles++;
            AddLogMessage($"✗ فشل في تصدير: {fileName} / Failed to export: {fileName}");
        }

        UpdateStatistics();
    }

    public void ExportCompleted()
    {
        Dispatcher.Invoke(() =>
        {
            OverallProgressBar.Value = 100;
            OverallProgressText.Text = "100%";
            CurrentFileText.Text = "اكتمل التصدير / Export completed";
            CurrentFileProgressBar.Value = 100;
            
            PauseResumeButton.Visibility = Visibility.Collapsed;
            CancelButton.Visibility = Visibility.Collapsed;
            CloseButton.Visibility = Visibility.Visible;
            
            var message = $"تم تصدير {_completedFiles} من {_totalFiles} صورة";
            if (_failedFiles > 0)
            {
                message += $"، فشل في {_failedFiles} صورة";
            }
            message += $" / Exported {_completedFiles} of {_totalFiles} photos";
            if (_failedFiles > 0)
            {
                message += $", {_failedFiles} failed";
            }
            
            AddLogMessage(message);
        });
    }

    private void UpdateStatistics()
    {
        Dispatcher.Invoke(() =>
        {
            CompletedCountText.Text = _completedFiles.ToString();
            FailedCountText.Text = _failedFiles.ToString();
            RemainingCountText.Text = (_totalFiles - _completedFiles - _failedFiles).ToString();
        });
    }

    private void UpdateTimeRemaining()
    {
        if (_completedFiles == 0) return;

        var elapsed = DateTime.Now - _startTime;
        var averageTimePerFile = elapsed.TotalSeconds / _completedFiles;
        var remainingFiles = _totalFiles - _completedFiles - _failedFiles;
        var estimatedTimeRemaining = TimeSpan.FromSeconds(averageTimePerFile * remainingFiles);

        Dispatcher.Invoke(() =>
        {
            if (estimatedTimeRemaining.TotalHours >= 1)
            {
                TimeRemainingText.Text = $"الوقت المتبقي: {estimatedTimeRemaining:hh\\:mm\\:ss} / Time remaining: {estimatedTimeRemaining:hh\\:mm\\:ss}";
            }
            else
            {
                TimeRemainingText.Text = $"الوقت المتبقي: {estimatedTimeRemaining:mm\\:ss} / Time remaining: {estimatedTimeRemaining:mm\\:ss}";
            }
        });
    }

    private void AddLogMessage(string message)
    {
        Dispatcher.Invoke(() =>
        {
            var timestamp = DateTime.Now.ToString("HH:mm:ss");
            LogTextBlock.Text += $"[{timestamp}] {message}\n";
            
            // التمرير إلى الأسفل
            // Scroll to bottom
            if (LogTextBlock.Parent is ScrollViewer scrollViewer)
            {
                scrollViewer.ScrollToEnd();
            }
        });
    }

    private void UpdateUI()
    {
        UpdateStatistics();
        TimeRemainingText.Text = "حساب الوقت المتبقي... / Calculating time remaining...";
    }

    private void PauseResumeButton_Click(object sender, RoutedEventArgs e)
    {
        _isPaused = !_isPaused;
        
        if (_isPaused)
        {
            PauseResumeButton.Content = "استئناف / Resume";
            AddLogMessage("تم إيقاف التصدير مؤقتاً / Export paused");
        }
        else
        {
            PauseResumeButton.Content = "إيقاف مؤقت / Pause";
            AddLogMessage("تم استئناف التصدير / Export resumed");
        }
    }

    private void CancelButton_Click(object sender, RoutedEventArgs e)
    {
        var result = MessageBox.Show(
            "هل أنت متأكد من إلغاء عملية التصدير؟\nAre you sure you want to cancel the export?",
            "تأكيد الإلغاء / Confirm Cancel",
            MessageBoxButton.YesNo,
            MessageBoxImage.Question);

        if (result == MessageBoxResult.Yes)
        {
            _isCancelled = true;
            AddLogMessage("تم إلغاء التصدير بواسطة المستخدم / Export cancelled by user");
            
            PauseResumeButton.Visibility = Visibility.Collapsed;
            CancelButton.Visibility = Visibility.Collapsed;
            CloseButton.Visibility = Visibility.Visible;
            
            CurrentFileText.Text = "تم الإلغاء / Cancelled";
        }
    }

    private void CloseButton_Click(object sender, RoutedEventArgs e)
    {
        DialogResult = true;
        Close();
    }

    protected override void OnClosing(CancelEventArgs e)
    {
        if (!_isCancelled && _completedFiles + _failedFiles < _totalFiles)
        {
            var result = MessageBox.Show(
                "التصدير لم يكتمل بعد. هل تريد إغلاق النافذة؟\nExport is not complete yet. Do you want to close the window?",
                "تأكيد الإغلاق / Confirm Close",
                MessageBoxButton.YesNo,
                MessageBoxImage.Warning);

            if (result == MessageBoxResult.No)
            {
                e.Cancel = true;
                return;
            }

            _isCancelled = true;
        }

        base.OnClosing(e);
    }
}
