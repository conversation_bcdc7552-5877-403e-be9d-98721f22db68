using ELashraffyEditor.Core.Models;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.PixelFormats;

namespace ELashraffyEditor.Scripting.Lua;

/// <summary>
/// واجهة خدمة البرمجة النصية بـ Lua
/// Lua scripting service interface
/// </summary>
public interface ILuaScriptingService
{
    /// <summary>
    /// تنفيذ سكريبت Lua
    /// Execute Lua script
    /// </summary>
    Task<object?> ExecuteScriptAsync(string script, Dictionary<string, object>? parameters = null);

    /// <summary>
    /// تنفيذ سكريبت من ملف
    /// Execute script from file
    /// </summary>
    Task<object?> ExecuteScriptFileAsync(string filePath, Dictionary<string, object>? parameters = null);

    /// <summary>
    /// تطبيق سكريبت على صورة
    /// Apply script to image
    /// </summary>
    Task<Image<Rgba32>?> ApplyImageScriptAsync(Image<Rgba32> image, string script);

    /// <summary>
    /// تطبيق سكريبت تعديل على صورة
    /// Apply adjustment script to image
    /// </summary>
    Task<ImageAdjustment?> ApplyAdjustmentScriptAsync(Image<Rgba32> image, string script);

    /// <summary>
    /// تحميل سكريبت من ملف
    /// Load script from file
    /// </summary>
    Task<string> LoadScriptAsync(string filePath);

    /// <summary>
    /// حفظ سكريبت إلى ملف
    /// Save script to file
    /// </summary>
    Task SaveScriptAsync(string filePath, string script);

    /// <summary>
    /// التحقق من صحة السكريبت
    /// Validate script syntax
    /// </summary>
    Task<bool> ValidateScriptAsync(string script);

    /// <summary>
    /// الحصول على السكريبتات المتاحة
    /// Get available scripts
    /// </summary>
    Task<string[]> GetAvailableScriptsAsync();

    /// <summary>
    /// تسجيل دالة C# للاستخدام في Lua
    /// Register C# function for use in Lua
    /// </summary>
    void RegisterFunction(string name, Delegate function);

    /// <summary>
    /// إزالة تسجيل دالة
    /// Unregister function
    /// </summary>
    void UnregisterFunction(string name);

    /// <summary>
    /// الحصول على الدوال المسجلة
    /// Get registered functions
    /// </summary>
    string[] GetRegisteredFunctions();

    /// <summary>
    /// تعيين متغير عام
    /// Set global variable
    /// </summary>
    void SetGlobalVariable(string name, object value);

    /// <summary>
    /// الحصول على متغير عام
    /// Get global variable
    /// </summary>
    T? GetGlobalVariable<T>(string name);

    /// <summary>
    /// مسح جميع المتغيرات العامة
    /// Clear all global variables
    /// </summary>
    void ClearGlobalVariables();

    /// <summary>
    /// تعيين مهلة زمنية للتنفيذ
    /// Set execution timeout
    /// </summary>
    void SetTimeout(TimeSpan timeout);

    /// <summary>
    /// تفعيل أو إلغاء تفعيل الوصول للملفات
    /// Enable or disable file access
    /// </summary>
    void SetFileAccessEnabled(bool enabled);

    /// <summary>
    /// تفعيل أو إلغاء تفعيل الوصول للشبكة
    /// Enable or disable network access
    /// </summary>
    void SetNetworkAccessEnabled(bool enabled);

    /// <summary>
    /// الحصول على معلومات البيئة
    /// Get environment information
    /// </summary>
    Dictionary<string, object> GetEnvironmentInfo();

    /// <summary>
    /// حدث تنفيذ السكريبت
    /// Script execution event
    /// </summary>
    event EventHandler<ScriptExecutionEventArgs>? ScriptExecuted;

    /// <summary>
    /// حدث خطأ في السكريبت
    /// Script error event
    /// </summary>
    event EventHandler<ScriptErrorEventArgs>? ScriptError;
}

/// <summary>
/// معاملات حدث تنفيذ السكريبت
/// Script execution event arguments
/// </summary>
public class ScriptExecutionEventArgs : EventArgs
{
    public string Script { get; set; } = string.Empty;
    public TimeSpan ExecutionTime { get; set; }
    public object? Result { get; set; }
    public Dictionary<string, object>? Parameters { get; set; }
}

/// <summary>
/// معاملات حدث خطأ السكريبت
/// Script error event arguments
/// </summary>
public class ScriptErrorEventArgs : EventArgs
{
    public string Script { get; set; } = string.Empty;
    public Exception Exception { get; set; } = null!;
    public string ErrorMessage { get; set; } = string.Empty;
    public int LineNumber { get; set; }
}
