using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;
using ELashraffyEditor.Core.Models;
using ELashraffyEditor.Core.Services;
using ELashraffyEditor.UI.Services;
using Microsoft.Extensions.Logging;
using System.IO;

namespace ELashraffyEditor.UI.ViewModels;

/// <summary>
/// ViewModel لعرض المكتبة
/// Library view model
/// </summary>
public partial class LibraryViewModel : ObservableObject
{
    private readonly ILogger<LibraryViewModel> _logger;
    private readonly IImageProcessingService _imageProcessingService;
    private readonly IDialogService _dialogService;
    private readonly ISettingsService _settingsService;

    [ObservableProperty]
    private ObservableCollection<PhotoItem> _photos = new();

    [ObservableProperty]
    private PhotoItem? _selectedPhoto;

    [ObservableProperty]
    private bool _isLoading;

    [ObservableProperty]
    private string _searchText = string.Empty;

    [ObservableProperty]
    private string _sortBy = "DateCreated";

    [ObservableProperty]
    private bool _sortDescending = true;

    [ObservableProperty]
    private string _filterBy = "All";

    [ObservableProperty]
    private int _thumbnailSize = 200;

    [ObservableProperty]
    private string _currentFolder = string.Empty;

    [ObservableProperty]
    private int _totalPhotos;

    [ObservableProperty]
    private int _selectedCount;

    public LibraryViewModel(
        ILogger<LibraryViewModel> logger,
        IImageProcessingService imageProcessingService,
        IDialogService dialogService,
        ISettingsService settingsService)
    {
        _logger = logger;
        _imageProcessingService = imageProcessingService;
        _dialogService = dialogService;
        _settingsService = settingsService;

        LoadSettings();
        _logger.LogInformation("LibraryViewModel initialized");
    }

    private void LoadSettings()
    {
        ThumbnailSize = _settingsService.GetSetting("ThumbnailSize", 200);
        CurrentFolder = _settingsService.GetSetting("LastOpenedFolder", 
            Environment.GetFolderPath(Environment.SpecialFolder.MyPictures));
    }

    [RelayCommand]
    private async Task ImportPhotosAsync()
    {
        try
        {
            IsLoading = true;
            
            var files = _dialogService.ShowOpenFilesDialog(
                "Image Files|*.jpg;*.jpeg;*.png;*.tiff;*.tif;*.bmp;*.gif;*.webp;*.cr2;*.nef;*.arw;*.dng",
                "اختر الصور / Select Images");

            if (files != null && files.Length > 0)
            {
                await ProcessImportedFilesAsync(files);
                
                // حفظ المجلد الأخير
                // Save last folder
                var lastFolder = Path.GetDirectoryName(files[0]);
                if (!string.IsNullOrEmpty(lastFolder))
                {
                    CurrentFolder = lastFolder;
                    _settingsService.SetSetting("LastOpenedFolder", lastFolder);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to import photos");
            _dialogService.ShowError($"فشل في استيراد الصور: {ex.Message}\nFailed to import photos: {ex.Message}");
        }
        finally
        {
            IsLoading = false;
        }
    }

    [RelayCommand]
    private async Task ImportFolderAsync()
    {
        try
        {
            IsLoading = true;
            
            var folder = _dialogService.ShowFolderDialog("اختر مجلد الصور / Select Photos Folder");
            if (!string.IsNullOrEmpty(folder))
            {
                CurrentFolder = folder;
                LoadPhotosFromFolderAsync(folder);
                
                _settingsService.SetSetting("LastOpenedFolder", folder);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to import folder");
            _dialogService.ShowError($"فشل في استيراد المجلد: {ex.Message}\nFailed to import folder: {ex.Message}");
        }
        finally
        {
            IsLoading = false;
        }
    }

    [RelayCommand]
    private void SelectPhoto(PhotoItem photo)
    {
        if (photo != null)
        {
            SelectedPhoto = photo;
            _logger.LogInformation("Photo selected: {FileName}", photo.FileName);
        }
    }

    [RelayCommand]
    private void TogglePhotoSelection(PhotoItem photo)
    {
        if (photo != null)
        {
            photo.IsSelected = !photo.IsSelected;
            UpdateSelectedCount();
        }
    }

    [RelayCommand]
    private void SelectAllPhotos()
    {
        foreach (var photo in Photos)
        {
            photo.IsSelected = true;
        }
        UpdateSelectedCount();
    }

    [RelayCommand]
    private void DeselectAllPhotos()
    {
        foreach (var photo in Photos)
        {
            photo.IsSelected = false;
        }
        UpdateSelectedCount();
    }

    [RelayCommand]
    private async Task DeleteSelectedPhotosAsync()
    {
        var selectedPhotos = Photos.Where(p => p.IsSelected).ToList();
        if (selectedPhotos.Count == 0)
        {
            _dialogService.ShowWarning("لم يتم اختيار أي صور للحذف\nNo photos selected for deletion");
            return;
        }

        var message = $"هل أنت متأكد من حذف {selectedPhotos.Count} صورة؟\nAre you sure you want to delete {selectedPhotos.Count} photos?";
        if (_dialogService.ShowConfirmation(message))
        {
            try
            {
                foreach (var photo in selectedPhotos)
                {
                    Photos.Remove(photo);
                }
                
                UpdateCounts();
                _logger.LogInformation("Deleted {Count} photos from library", selectedPhotos.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to delete photos");
                _dialogService.ShowError($"فشل في حذف الصور: {ex.Message}\nFailed to delete photos: {ex.Message}");
            }
        }
    }

    [RelayCommand]
    private async Task RefreshLibraryAsync()
    {
        try
        {
            IsLoading = true;
            
            if (!string.IsNullOrEmpty(CurrentFolder) && Directory.Exists(CurrentFolder))
            {
                LoadPhotosFromFolderAsync(CurrentFolder);
            }
            
            _logger.LogInformation("Library refreshed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to refresh library");
            _dialogService.ShowError($"فشل في تحديث المكتبة: {ex.Message}\nFailed to refresh library: {ex.Message}");
        }
        finally
        {
            IsLoading = false;
        }
    }

    [RelayCommand]
    private void ChangeThumbnailSize(int size)
    {
        ThumbnailSize = Math.Clamp(size, 100, 400);
        _settingsService.SetSetting("ThumbnailSize", ThumbnailSize);
        
        // إعادة إنشاء الصور المصغرة إذا لزم الأمر
        // Regenerate thumbnails if needed
        _ = Task.Run(async () => await RegenerateThumbnailsAsync());
    }

    [RelayCommand]
    private void SortPhotos(string sortBy)
    {
        SortBy = sortBy;
        ApplySorting();
    }

    [RelayCommand]
    private void ToggleSortDirection()
    {
        SortDescending = !SortDescending;
        ApplySorting();
    }

    [RelayCommand]
    private void FilterPhotos(string filterBy)
    {
        FilterBy = filterBy;
        ApplyFiltering();
    }

    [RelayCommand]
    private void SearchPhotos()
    {
        ApplyFiltering();
    }

    private async Task ProcessImportedFilesAsync(string[] files)
    {
        var progressDialog = _dialogService.ShowProgressDialog(
            "استيراد الصور / Importing Photos",
            "جاري معالجة الملفات... / Processing files...",
            true);

        try
        {
            for (int i = 0; i < files.Length; i++)
            {
                if (progressDialog.IsCancelled) break;

                var file = files[i];
                var progress = (int)((double)(i + 1) / files.Length * 100);
                progressDialog.UpdateProgress(progress, $"معالجة: {Path.GetFileName(file)} / Processing: {Path.GetFileName(file)}");

                var photoItem = await _imageProcessingService.GetImageInfoAsync(file);
                if (photoItem != null)
                {
                    Photos.Add(photoItem);
                }

                await Task.Delay(10); // Small delay to allow UI updates
            }

            UpdateCounts();
            ApplySorting();
        }
        finally
        {
            progressDialog.Close();
        }
    }

    private void LoadPhotosFromFolderAsync(string folderPath)
    {
        if (!Directory.Exists(folderPath)) return;

        Photos.Clear();
        
        var supportedExtensions = _imageProcessingService.GetSupportedFormats();
        var files = Directory.GetFiles(folderPath, "*.*", SearchOption.AllDirectories)
            .Where(file => supportedExtensions.Any(ext => 
                file.EndsWith(ext, StringComparison.OrdinalIgnoreCase)))
            .ToArray();

        Task.Run(() => ProcessImportedFilesAsync(files));
    }

    private async Task RegenerateThumbnailsAsync()
    {
        foreach (var photo in Photos)
        {
            try
            {
                photo.ThumbnailData = await _imageProcessingService.GenerateThumbnailAsync(
                    photo.FilePath, ThumbnailSize, ThumbnailSize);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to regenerate thumbnail for {FileName}", photo.FileName);
            }
        }
    }

    private void ApplySorting()
    {
        var sortedPhotos = SortBy switch
        {
            "FileName" => SortDescending 
                ? Photos.OrderByDescending(p => p.FileName)
                : Photos.OrderBy(p => p.FileName),
            "DateCreated" => SortDescending 
                ? Photos.OrderByDescending(p => p.DateCreated)
                : Photos.OrderBy(p => p.DateCreated),
            "DateModified" => SortDescending 
                ? Photos.OrderByDescending(p => p.DateModified)
                : Photos.OrderBy(p => p.DateModified),
            "FileSize" => SortDescending 
                ? Photos.OrderByDescending(p => p.FileSize)
                : Photos.OrderBy(p => p.FileSize),
            _ => Photos.OrderByDescending(p => p.DateCreated)
        };

        var sortedList = sortedPhotos.ToList();
        Photos.Clear();
        foreach (var photo in sortedList)
        {
            Photos.Add(photo);
        }
    }

    private void ApplyFiltering()
    {
        // تطبيق الفلترة والبحث
        // Apply filtering and search
        // هذا مبسط - يمكن تحسينه لاحقاً
        // This is simplified - can be improved later
    }

    private void UpdateSelectedCount()
    {
        SelectedCount = Photos.Count(p => p.IsSelected);
    }

    private void UpdateCounts()
    {
        TotalPhotos = Photos.Count;
        UpdateSelectedCount();
    }
}
