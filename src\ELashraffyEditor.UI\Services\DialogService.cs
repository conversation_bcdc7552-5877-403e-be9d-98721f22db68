using Microsoft.Extensions.Logging;
using Microsoft.Win32;
using System.Windows;

namespace ELashraffyEditor.UI.Services;

/// <summary>
/// خدمة الحوارات
/// Dialog service
/// </summary>
public class DialogService : IDialogService
{
    private readonly ILogger<DialogService> _logger;

    public DialogService(ILogger<DialogService> logger)
    {
        _logger = logger;
    }

    public void ShowInformation(string message, string title = "Information")
    {
        try
        {
            System.Windows.MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Information);
            _logger.LogDebug("Information dialog shown: {Title}", title);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to show information dialog");
        }
    }

    public void ShowWarning(string message, string title = "Warning")
    {
        try
        {
            System.Windows.MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Warning);
            _logger.LogDebug("Warning dialog shown: {Title}", title);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to show warning dialog");
        }
    }

    public void ShowError(string message, string title = "Error")
    {
        try
        {
            System.Windows.MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Error);
            _logger.LogDebug("Error dialog shown: {Title}", title);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to show error dialog");
        }
    }

    public bool ShowConfirmation(string message, string title = "Confirmation")
    {
        try
        {
            var result = System.Windows.MessageBox.Show(message, title, MessageBoxButton.YesNo, MessageBoxImage.Question);
            _logger.LogDebug("Confirmation dialog shown: {Title}, Result: {Result}", title, result);
            return result == MessageBoxResult.Yes;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to show confirmation dialog");
            return false;
        }
    }

    public string? ShowOpenFileDialog(string filter = "", string title = "Open File")
    {
        try
        {
            var dialog = new Microsoft.Win32.OpenFileDialog
            {
                Title = title,
                Filter = string.IsNullOrEmpty(filter) ? GetDefaultImageFilter() : filter,
                Multiselect = false
            };

            var result = dialog.ShowDialog();
            _logger.LogDebug("Open file dialog shown: {Title}, Result: {Result}", title, result);
            
            return result == true ? dialog.FileName : null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to show open file dialog");
            return null;
        }
    }

    public string[]? ShowOpenFilesDialog(string filter = "", string title = "Open Files")
    {
        try
        {
            var dialog = new Microsoft.Win32.OpenFileDialog
            {
                Title = title,
                Filter = string.IsNullOrEmpty(filter) ? GetDefaultImageFilter() : filter,
                Multiselect = true
            };

            var result = dialog.ShowDialog();
            _logger.LogDebug("Open files dialog shown: {Title}, Result: {Result}", title, result);
            
            return result == true ? dialog.FileNames : null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to show open files dialog");
            return null;
        }
    }

    public string? ShowSaveFileDialog(string filter = "", string title = "Save File", string defaultFileName = "")
    {
        try
        {
            var dialog = new Microsoft.Win32.SaveFileDialog
            {
                Title = title,
                Filter = string.IsNullOrEmpty(filter) ? GetDefaultImageFilter() : filter,
                FileName = defaultFileName
            };

            var result = dialog.ShowDialog();
            _logger.LogDebug("Save file dialog shown: {Title}, Result: {Result}", title, result);
            
            return result == true ? dialog.FileName : null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to show save file dialog");
            return null;
        }
    }

    public string? ShowFolderDialog(string title = "Select Folder")
    {
        try
        {
            // استخدام OpenFileDialog مع خيار اختيار المجلدات
            // Use OpenFileDialog with folder selection option
            var dialog = new OpenFileDialog
            {
                Title = title,
                CheckFileExists = false,
                CheckPathExists = true,
                FileName = "Folder Selection",
                Filter = "Folders|*.folder",
                ValidateNames = false
            };

            var result = dialog.ShowDialog();
            _logger.LogDebug("Folder dialog shown: {Title}, Result: {Result}", title, result);

            if (result == true && !string.IsNullOrEmpty(dialog.FileName))
            {
                return System.IO.Path.GetDirectoryName(dialog.FileName);
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to show folder dialog");
            return null;
        }
    }

    public string? ShowTextInputDialog(string message, string title = "Input", string defaultValue = "")
    {
        try
        {
            // إنشاء حوار إدخال نص بسيط
            // Create simple text input dialog
            var inputDialog = new TextInputDialog(message, title, defaultValue);
            var result = inputDialog.ShowDialog();
            
            _logger.LogDebug("Text input dialog shown: {Title}, Result: {Result}", title, result);
            
            return result == true ? inputDialog.InputText : null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to show text input dialog");
            return null;
        }
    }

    public IProgressDialog ShowProgressDialog(string title, string message, bool canCancel = false)
    {
        try
        {
            var progressDialog = new ProgressDialogImpl(title, message, canCancel);
            progressDialog.Show();
            
            _logger.LogDebug("Progress dialog shown: {Title}", title);
            
            return progressDialog;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to show progress dialog");
            return new DummyProgressDialog();
        }
    }

    private string GetDefaultImageFilter()
    {
        return "Image Files|*.jpg;*.jpeg;*.png;*.tiff;*.tif;*.bmp;*.gif;*.webp|" +
               "JPEG Files|*.jpg;*.jpeg|" +
               "PNG Files|*.png|" +
               "TIFF Files|*.tiff;*.tif|" +
               "BMP Files|*.bmp|" +
               "GIF Files|*.gif|" +
               "WebP Files|*.webp|" +
               "RAW Files|*.cr2;*.nef;*.arw;*.dng;*.raf;*.orf;*.rw2|" +
               "All Files|*.*";
    }
}

/// <summary>
/// حوار إدخال النص
/// Text input dialog
/// </summary>
public class TextInputDialog : Window
{
    private readonly System.Windows.Controls.TextBox _textBox;
    
    public string InputText => _textBox.Text;

    public TextInputDialog(string message, string title, string defaultValue)
    {
        Title = title;
        Width = 400;
        Height = 200;
        WindowStartupLocation = WindowStartupLocation.CenterOwner;
        ResizeMode = ResizeMode.NoResize;

        var grid = new System.Windows.Controls.Grid();
        grid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition { Height = System.Windows.GridLength.Auto });
        grid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition { Height = System.Windows.GridLength.Auto });
        grid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition { Height = System.Windows.GridLength.Auto });

        var messageLabel = new System.Windows.Controls.Label
        {
            Content = message,
            Margin = new Thickness(10)
        };
        System.Windows.Controls.Grid.SetRow(messageLabel, 0);
        grid.Children.Add(messageLabel);

        _textBox = new System.Windows.Controls.TextBox
        {
            Text = defaultValue,
            Margin = new Thickness(10),
            Padding = new Thickness(5)
        };
        System.Windows.Controls.Grid.SetRow(_textBox, 1);
        grid.Children.Add(_textBox);

        var buttonPanel = new System.Windows.Controls.StackPanel
        {
            Orientation = System.Windows.Controls.Orientation.Horizontal,
            HorizontalAlignment = System.Windows.HorizontalAlignment.Right,
            Margin = new Thickness(10)
        };

        var okButton = new System.Windows.Controls.Button
        {
            Content = "OK",
            Width = 75,
            Height = 25,
            Margin = new Thickness(5, 0, 0, 0),
            IsDefault = true
        };
        okButton.Click += (s, e) => { DialogResult = true; Close(); };

        var cancelButton = new System.Windows.Controls.Button
        {
            Content = "Cancel",
            Width = 75,
            Height = 25,
            IsCancel = true
        };
        cancelButton.Click += (s, e) => { DialogResult = false; Close(); };

        buttonPanel.Children.Add(cancelButton);
        buttonPanel.Children.Add(okButton);

        System.Windows.Controls.Grid.SetRow(buttonPanel, 2);
        grid.Children.Add(buttonPanel);

        Content = grid;
        _textBox.Focus();
        _textBox.SelectAll();
    }
}

/// <summary>
/// تنفيذ حوار شريط التقدم
/// Progress dialog implementation
/// </summary>
public class ProgressDialogImpl : Window, IProgressDialog, IDisposable
{
    public void Dispose()
    {
        Close();
    }
    private readonly System.Windows.Controls.ProgressBar _progressBar;
    private readonly System.Windows.Controls.TextBlock _messageText;
    private readonly System.Windows.Controls.Button? _cancelButton;
    private bool _isCancelled;

    public bool IsCancelled => _isCancelled;

    public ProgressDialogImpl(string title, string message, bool canCancel)
    {
        Title = title;
        Width = 400;
        Height = 150;
        WindowStartupLocation = WindowStartupLocation.CenterOwner;
        ResizeMode = ResizeMode.NoResize;

        var grid = new System.Windows.Controls.Grid();
        grid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition { Height = System.Windows.GridLength.Auto });
        grid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition { Height = System.Windows.GridLength.Auto });
        if (canCancel)
            grid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition { Height = System.Windows.GridLength.Auto });

        _messageText = new System.Windows.Controls.TextBlock
        {
            Text = message,
            Margin = new Thickness(10),
            TextWrapping = TextWrapping.Wrap
        };
        System.Windows.Controls.Grid.SetRow(_messageText, 0);
        grid.Children.Add(_messageText);

        _progressBar = new System.Windows.Controls.ProgressBar
        {
            Height = 20,
            Margin = new Thickness(10),
            Minimum = 0,
            Maximum = 100
        };
        System.Windows.Controls.Grid.SetRow(_progressBar, 1);
        grid.Children.Add(_progressBar);

        if (canCancel)
        {
            _cancelButton = new System.Windows.Controls.Button
            {
                Content = "Cancel",
                Width = 75,
                Height = 25,
                HorizontalAlignment = System.Windows.HorizontalAlignment.Center,
                Margin = new Thickness(10)
            };
            _cancelButton.Click += (s, e) => { _isCancelled = true; Close(); };
            System.Windows.Controls.Grid.SetRow(_cancelButton, 2);
            grid.Children.Add(_cancelButton);
        }

        Content = grid;
    }

    public void UpdateProgress(int percentage, string message = "")
    {
        Dispatcher.Invoke(() =>
        {
            _progressBar.Value = Math.Clamp(percentage, 0, 100);
            if (!string.IsNullOrEmpty(message))
            {
                _messageText.Text = message;
            }
        });
    }

    public new void Close()
    {
        Dispatcher.Invoke(() => base.Close());
    }
}

/// <summary>
/// حوار تقدم وهمي للحالات الطارئة
/// Dummy progress dialog for emergency cases
/// </summary>
public class DummyProgressDialog : IProgressDialog
{
    public bool IsCancelled => false;
    public void UpdateProgress(int percentage, string message = "") { }
    public void Close() { }
    public void Dispose() { }
}
