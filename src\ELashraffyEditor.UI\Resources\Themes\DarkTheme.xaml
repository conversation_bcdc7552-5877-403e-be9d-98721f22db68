<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- Dark Theme Color Palette -->
    
    <!-- Background Colors -->
    <Color x:Key="PrimaryBackgroundColor">#FF1E1E1E</Color>
    <Color x:Key="SecondaryBackgroundColor">#FF2D2D30</Color>
    <Color x:Key="TertiaryBackgroundColor">#FF3E3E42</Color>
    <Color x:Key="QuaternaryBackgroundColor">#FF4A4A4F</Color>
    
    <!-- Text Colors -->
    <Color x:Key="PrimaryTextColor">#FFFFFFFF</Color>
    <Color x:Key="SecondaryTextColor">#FFB0B0B0</Color>
    <Color x:Key="TertiaryTextColor">#FF808080</Color>
    <Color x:Key="DisabledTextColor">#FF606060</Color>
    
    <!-- Accent Colors -->
    <Color x:Key="AccentColor">#FF673AB7</Color>
    <Color x:Key="AccentHoverColor">#FF7C4DFF</Color>
    <Color x:Key="AccentPressedColor">#FF5E35B1</Color>
    <Color x:Key="AccentDisabledColor">#FF4A4A4F</Color>
    
    <!-- Semantic Colors -->
    <Color x:Key="SuccessColor">#FF4CAF50</Color>
    <Color x:Key="WarningColor">#FFFF9800</Color>
    <Color x:Key="ErrorColor">#FFF44336</Color>
    <Color x:Key="InfoColor">#FF2196F3</Color>
    
    <!-- Border and Separator Colors -->
    <Color x:Key="BorderColor">#FF404040</Color>
    <Color x:Key="SeparatorColor">#FF333333</Color>
    <Color x:Key="HighlightColor">#FF2196F3</Color>
    <Color x:Key="SelectionColor">#FF3F51B5</Color>
    
    <!-- Convert Colors to Brushes -->
    <SolidColorBrush x:Key="PrimaryBackgroundBrush" Color="{StaticResource PrimaryBackgroundColor}"/>
    <SolidColorBrush x:Key="SecondaryBackgroundBrush" Color="{StaticResource SecondaryBackgroundColor}"/>
    <SolidColorBrush x:Key="TertiaryBackgroundBrush" Color="{StaticResource TertiaryBackgroundColor}"/>
    <SolidColorBrush x:Key="QuaternaryBackgroundBrush" Color="{StaticResource QuaternaryBackgroundColor}"/>
    
    <SolidColorBrush x:Key="PrimaryTextBrush" Color="{StaticResource PrimaryTextColor}"/>
    <SolidColorBrush x:Key="SecondaryTextBrush" Color="{StaticResource SecondaryTextColor}"/>
    <SolidColorBrush x:Key="TertiaryTextBrush" Color="{StaticResource TertiaryTextColor}"/>
    <SolidColorBrush x:Key="DisabledTextBrush" Color="{StaticResource DisabledTextColor}"/>
    
    <SolidColorBrush x:Key="AccentBrush" Color="{StaticResource AccentColor}"/>
    <SolidColorBrush x:Key="AccentHoverBrush" Color="{StaticResource AccentHoverColor}"/>
    <SolidColorBrush x:Key="AccentPressedBrush" Color="{StaticResource AccentPressedColor}"/>
    <SolidColorBrush x:Key="AccentDisabledBrush" Color="{StaticResource AccentDisabledColor}"/>
    
    <SolidColorBrush x:Key="SuccessBrush" Color="{StaticResource SuccessColor}"/>
    <SolidColorBrush x:Key="WarningBrush" Color="{StaticResource WarningColor}"/>
    <SolidColorBrush x:Key="ErrorBrush" Color="{StaticResource ErrorColor}"/>
    <SolidColorBrush x:Key="InfoBrush" Color="{StaticResource InfoColor}"/>
    
    <SolidColorBrush x:Key="BorderBrush" Color="{StaticResource BorderColor}"/>
    <SolidColorBrush x:Key="SeparatorBrush" Color="{StaticResource SeparatorColor}"/>
    <SolidColorBrush x:Key="HighlightBrush" Color="{StaticResource HighlightColor}"/>
    <SolidColorBrush x:Key="SelectionBrush" Color="{StaticResource SelectionColor}"/>
    
    <!-- Gradient Brushes -->
    <LinearGradientBrush x:Key="HeaderGradientBrush" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="{StaticResource SecondaryBackgroundColor}" Offset="0"/>
        <GradientStop Color="{StaticResource TertiaryBackgroundColor}" Offset="1"/>
    </LinearGradientBrush>
    
    <LinearGradientBrush x:Key="AccentGradientBrush" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="{StaticResource AccentColor}" Offset="0"/>
        <GradientStop Color="{StaticResource AccentPressedColor}" Offset="1"/>
    </LinearGradientBrush>
    
    <!-- Shadow Effects -->
    <DropShadowEffect x:Key="DefaultShadow" 
                      Color="Black" 
                      Direction="270" 
                      ShadowDepth="2" 
                      BlurRadius="4" 
                      Opacity="0.3"/>
    
    <DropShadowEffect x:Key="ElevatedShadow" 
                      Color="Black" 
                      Direction="270" 
                      ShadowDepth="4" 
                      BlurRadius="8" 
                      Opacity="0.4"/>
    
    <DropShadowEffect x:Key="FloatingShadow" 
                      Color="Black" 
                      Direction="270" 
                      ShadowDepth="8" 
                      BlurRadius="16" 
                      Opacity="0.5"/>
    
    <!-- Animation Durations -->
    <Duration x:Key="FastAnimation">0:0:0.15</Duration>
    <Duration x:Key="NormalAnimation">0:0:0.25</Duration>
    <Duration x:Key="SlowAnimation">0:0:0.4</Duration>
    
    <!-- Easing Functions -->
    <CubicEase x:Key="EaseOut" EasingMode="EaseOut"/>
    <CubicEase x:Key="EaseIn" EasingMode="EaseIn"/>
    <CubicEase x:Key="EaseInOut" EasingMode="EaseInOut"/>
    
    <!-- Common Dimensions -->
    <sys:Double x:Key="SmallIconSize" xmlns:sys="clr-namespace:System;assembly=mscorlib">16</sys:Double>
    <sys:Double x:Key="MediumIconSize" xmlns:sys="clr-namespace:System;assembly=mscorlib">24</sys:Double>
    <sys:Double x:Key="LargeIconSize" xmlns:sys="clr-namespace:System;assembly=mscorlib">32</sys:Double>
    <sys:Double x:Key="ExtraLargeIconSize" xmlns:sys="clr-namespace:System;assembly=mscorlib">48</sys:Double>
    
    <sys:Double x:Key="SmallSpacing" xmlns:sys="clr-namespace:System;assembly=mscorlib">4</sys:Double>
    <sys:Double x:Key="MediumSpacing" xmlns:sys="clr-namespace:System;assembly=mscorlib">8</sys:Double>
    <sys:Double x:Key="LargeSpacing" xmlns:sys="clr-namespace:System;assembly=mscorlib">16</sys:Double>
    <sys:Double x:Key="ExtraLargeSpacing" xmlns:sys="clr-namespace:System;assembly=mscorlib">24</sys:Double>
    
    <!-- Corner Radius -->
    <CornerRadius x:Key="SmallCornerRadius">2</CornerRadius>
    <CornerRadius x:Key="MediumCornerRadius">4</CornerRadius>
    <CornerRadius x:Key="LargeCornerRadius">8</CornerRadius>
    <CornerRadius x:Key="ExtraLargeCornerRadius">12</CornerRadius>
    
    <!-- Border Thickness -->
    <Thickness x:Key="ThinBorder">1</Thickness>
    <Thickness x:Key="MediumBorder">2</Thickness>
    <Thickness x:Key="ThickBorder">3</Thickness>
    
    <!-- Margins and Paddings -->
    <Thickness x:Key="SmallMargin">4</Thickness>
    <Thickness x:Key="MediumMargin">8</Thickness>
    <Thickness x:Key="LargeMargin">16</Thickness>
    <Thickness x:Key="ExtraLargeMargin">24</Thickness>
    
    <Thickness x:Key="SmallPadding">6</Thickness>
    <Thickness x:Key="MediumPadding">12</Thickness>
    <Thickness x:Key="LargePadding">20</Thickness>
    <Thickness x:Key="ExtraLargePadding">32</Thickness>

</ResourceDictionary>
