using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace ELashraffyEditor.Core.Models;

/// <summary>
/// نموذج لتعديلات الصورة مع دعم الإشعارات
/// Image adjustment model with notification support
/// </summary>
public class ImageAdjustment : INotifyPropertyChanged
{
    private float _exposure = 0f;
    private float _contrast = 0f;
    private float _saturation = 0f;
    private float _sharpness = 0f;
    private float _shadows = 0f;
    private float _highlights = 0f;
    private float _brightness = 0f;
    private float _vibrance = 0f;
    private float _clarity = 0f;
    private float _temperature = 0f;
    private float _tint = 0f;

    /// <summary>
    /// التعرض - Exposure (-2.0 to +2.0)
    /// </summary>
    public float Exposure
    {
        get => _exposure;
        set => SetProperty(ref _exposure, Math.Clamp(value, -2.0f, 2.0f));
    }

    /// <summary>
    /// التباين - Contrast (-100 to +100)
    /// </summary>
    public float Contrast
    {
        get => _contrast;
        set => SetProperty(ref _contrast, Math.Clamp(value, -100f, 100f));
    }

    /// <summary>
    /// التشبع - Saturation (-100 to +100)
    /// </summary>
    public float Saturation
    {
        get => _saturation;
        set => SetProperty(ref _saturation, Math.Clamp(value, -100f, 100f));
    }

    /// <summary>
    /// الحدة - Sharpness (0 to +100)
    /// </summary>
    public float Sharpness
    {
        get => _sharpness;
        set => SetProperty(ref _sharpness, Math.Clamp(value, 0f, 100f));
    }

    /// <summary>
    /// الظلال - Shadows (-100 to +100)
    /// </summary>
    public float Shadows
    {
        get => _shadows;
        set => SetProperty(ref _shadows, Math.Clamp(value, -100f, 100f));
    }

    /// <summary>
    /// الإضاءات العالية - Highlights (-100 to +100)
    /// </summary>
    public float Highlights
    {
        get => _highlights;
        set => SetProperty(ref _highlights, Math.Clamp(value, -100f, 100f));
    }

    /// <summary>
    /// السطوع - Brightness (-100 to +100)
    /// </summary>
    public float Brightness
    {
        get => _brightness;
        set => SetProperty(ref _brightness, Math.Clamp(value, -100f, 100f));
    }

    /// <summary>
    /// الحيوية - Vibrance (-100 to +100)
    /// </summary>
    public float Vibrance
    {
        get => _vibrance;
        set => SetProperty(ref _vibrance, Math.Clamp(value, -100f, 100f));
    }

    /// <summary>
    /// الوضوح - Clarity (-100 to +100)
    /// </summary>
    public float Clarity
    {
        get => _clarity;
        set => SetProperty(ref _clarity, Math.Clamp(value, -100f, 100f));
    }

    /// <summary>
    /// درجة الحرارة - Temperature (2000K to 50000K)
    /// </summary>
    public float Temperature
    {
        get => _temperature;
        set => SetProperty(ref _temperature, Math.Clamp(value, -100f, 100f));
    }

    /// <summary>
    /// الصبغة - Tint (-100 to +100)
    /// </summary>
    public float Tint
    {
        get => _tint;
        set => SetProperty(ref _tint, Math.Clamp(value, -100f, 100f));
    }

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }

    protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
    {
        if (EqualityComparer<T>.Default.Equals(field, value)) return false;
        field = value;
        OnPropertyChanged(propertyName);
        return true;
    }

    /// <summary>
    /// إعادة تعيين جميع القيم للافتراضي
    /// Reset all values to default
    /// </summary>
    public void Reset()
    {
        Exposure = 0f;
        Contrast = 0f;
        Saturation = 0f;
        Sharpness = 0f;
        Shadows = 0f;
        Highlights = 0f;
        Brightness = 0f;
        Vibrance = 0f;
        Clarity = 0f;
        Temperature = 0f;
        Tint = 0f;
    }

    /// <summary>
    /// نسخ الإعدادات من تعديل آخر
    /// Copy settings from another adjustment
    /// </summary>
    public void CopyFrom(ImageAdjustment other)
    {
        Exposure = other.Exposure;
        Contrast = other.Contrast;
        Saturation = other.Saturation;
        Sharpness = other.Sharpness;
        Shadows = other.Shadows;
        Highlights = other.Highlights;
        Brightness = other.Brightness;
        Vibrance = other.Vibrance;
        Clarity = other.Clarity;
        Temperature = other.Temperature;
        Tint = other.Tint;
    }
}
