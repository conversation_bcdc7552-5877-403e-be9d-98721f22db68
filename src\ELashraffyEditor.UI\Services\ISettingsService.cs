namespace ELashraffyEditor.UI.Services;

/// <summary>
/// واجهة خدمة الإعدادات
/// Settings service interface
/// </summary>
public interface ISettingsService
{
    /// <summary>
    /// تحميل الإعدادات
    /// Load settings
    /// </summary>
    Dictionary<string, object> LoadSettings();

    /// <summary>
    /// حفظ الإعدادات
    /// Save settings
    /// </summary>
    void SaveSettings();

    /// <summary>
    /// حفظ الإعدادات مع قاموس محدد
    /// Save settings with specific dictionary
    /// </summary>
    void SaveSettings(Dictionary<string, object> settings);

    /// <summary>
    /// الحصول على قيمة إعداد
    /// Get setting value
    /// </summary>
    T GetSetting<T>(string key, T defaultValue = default!);

    /// <summary>
    /// تعيين قيمة إعداد
    /// Set setting value
    /// </summary>
    void SetSetting<T>(string key, T value);

    /// <summary>
    /// حذف إعداد
    /// Remove setting
    /// </summary>
    void RemoveSetting(string key);

    /// <summary>
    /// التحقق من وجود إعداد
    /// Check if setting exists
    /// </summary>
    bool HasSetting(string key);

    /// <summary>
    /// إعادة تعيين الإعدادات للافتراضي
    /// Reset settings to default
    /// </summary>
    void ResetToDefaults();

    /// <summary>
    /// الحصول على مسار ملف الإعدادات
    /// Get settings file path
    /// </summary>
    string GetSettingsFilePath();
}
