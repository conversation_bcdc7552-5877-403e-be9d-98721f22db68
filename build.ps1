# سكريبت البناء لمحرر الأشرافي
# Build script for ELashrafy Editor

param(
    [string]$Configuration = "Release",
    [string]$Platform = "x64",
    [switch]$Clean,
    [switch]$Test,
    [switch]$Package,
    [switch]$Publish,
    [switch]$BuildRust,
    [switch]$InstallPython,
    [string]$OutputPath = ".\dist"
)

# الألوان للإخراج
# Colors for output
$ErrorColor = "Red"
$WarningColor = "Yellow"
$InfoColor = "Green"
$HeaderColor = "Cyan"

function Write-Header {
    param([string]$Message)
    Write-Host "`n=== $Message ===" -ForegroundColor $HeaderColor
}

function Write-Info {
    param([string]$Message)
    Write-Host $Message -ForegroundColor $InfoColor
}

function Write-Warning {
    param([string]$Message)
    Write-Host $Message -ForegroundColor $WarningColor
}

function Write-Error {
    param([string]$Message)
    Write-Host $Message -ForegroundColor $ErrorColor
}

function Test-Command {
    param([string]$Command)
    try {
        Get-Command $Command -ErrorAction Stop | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

function Build-RustLibrary {
    Write-Header "بناء مكتبة Rust / Building Rust Library"
    
    if (-not (Test-Command "cargo")) {
        Write-Error "Rust/Cargo غير مثبت / Rust/Cargo not installed"
        return $false
    }
    
    $rustPath = "src\native\rust-processing"
    if (-not (Test-Path $rustPath)) {
        Write-Error "مجلد Rust غير موجود / Rust directory not found: $rustPath"
        return $false
    }
    
    Push-Location $rustPath
    try {
        Write-Info "بناء مكتبة Rust... / Building Rust library..."
        cargo build --release
        if ($LASTEXITCODE -ne 0) {
            Write-Error "فشل في بناء مكتبة Rust / Failed to build Rust library"
            return $false
        }
        
        # نسخ المكتبة المبنية
        # Copy built library
        $libSource = "target\release\elashrafy_image_processing.dll"
        $libDest = "..\..\ELashraffyEditor.UI\bin\$Configuration\net6.0-windows\"
        
        if (Test-Path $libSource) {
            New-Item -ItemType Directory -Force -Path $libDest | Out-Null
            Copy-Item $libSource $libDest -Force
            Write-Info "تم نسخ مكتبة Rust بنجاح / Rust library copied successfully"
        }
        
        return $true
    }
    finally {
        Pop-Location
    }
}

function Install-PythonDependencies {
    Write-Header "تثبيت تبعيات Python / Installing Python Dependencies"
    
    if (-not (Test-Command "python")) {
        Write-Error "Python غير مثبت / Python not installed"
        return $false
    }
    
    $pythonPath = "src\native\python-ai"
    if (-not (Test-Path "$pythonPath\requirements.txt")) {
        Write-Error "ملف requirements.txt غير موجود / requirements.txt not found"
        return $false
    }
    
    Push-Location $pythonPath
    try {
        Write-Info "تثبيت مكتبات Python... / Installing Python libraries..."
        python -m pip install -r requirements.txt
        if ($LASTEXITCODE -ne 0) {
            Write-Error "فشل في تثبيت مكتبات Python / Failed to install Python libraries"
            return $false
        }
        
        Write-Info "تم تثبيت مكتبات Python بنجاح / Python libraries installed successfully"
        return $true
    }
    finally {
        Pop-Location
    }
}

function Clean-Solution {
    Write-Header "تنظيف الحل / Cleaning Solution"
    
    Write-Info "حذف مجلدات bin و obj... / Removing bin and obj directories..."
    Get-ChildItem -Path . -Recurse -Directory -Name "bin" | Remove-Item -Recurse -Force
    Get-ChildItem -Path . -Recurse -Directory -Name "obj" | Remove-Item -Recurse -Force
    
    if (Test-Path $OutputPath) {
        Write-Info "حذف مجلد الإخراج... / Removing output directory..."
        Remove-Item $OutputPath -Recurse -Force
    }
    
    Write-Info "تم التنظيف بنجاح / Cleanup completed"
}

function Restore-Packages {
    Write-Header "استعادة الحزم / Restoring Packages"
    
    Write-Info "استعادة حزم NuGet... / Restoring NuGet packages..."
    dotnet restore
    if ($LASTEXITCODE -ne 0) {
        Write-Error "فشل في استعادة الحزم / Failed to restore packages"
        return $false
    }
    
    Write-Info "تم استعادة الحزم بنجاح / Packages restored successfully"
    return $true
}

function Build-Solution {
    Write-Header "بناء الحل / Building Solution"
    
    Write-Info "بناء المشروع... / Building project..."
    dotnet build --configuration $Configuration --no-restore
    if ($LASTEXITCODE -ne 0) {
        Write-Error "فشل في بناء المشروع / Failed to build project"
        return $false
    }
    
    Write-Info "تم بناء المشروع بنجاح / Project built successfully"
    return $true
}

function Run-Tests {
    Write-Header "تشغيل الاختبارات / Running Tests"
    
    Write-Info "تشغيل اختبارات الوحدة... / Running unit tests..."
    dotnet test --configuration $Configuration --no-build --verbosity normal
    if ($LASTEXITCODE -ne 0) {
        Write-Error "فشلت بعض الاختبارات / Some tests failed"
        return $false
    }
    
    Write-Info "نجحت جميع الاختبارات / All tests passed"
    return $true
}

function Publish-Application {
    Write-Header "نشر التطبيق / Publishing Application"
    
    $publishPath = "$OutputPath\publish"
    New-Item -ItemType Directory -Force -Path $publishPath | Out-Null
    
    Write-Info "نشر التطبيق... / Publishing application..."
    dotnet publish src\ELashraffyEditor.UI\ELashraffyEditor.UI.csproj `
        --configuration $Configuration `
        --runtime win-$Platform `
        --self-contained true `
        --output $publishPath `
        -p:PublishSingleFile=true `
        -p:PublishReadyToRun=true `
        -p:IncludeNativeLibrariesForSelfExtract=true
    
    if ($LASTEXITCODE -ne 0) {
        Write-Error "فشل في نشر التطبيق / Failed to publish application"
        return $false
    }
    
    # نسخ الملفات الإضافية
    # Copy additional files
    Copy-Item "src\ELashraffyEditor.WebUI" "$publishPath\src\ELashraffyEditor.WebUI" -Recurse -Force
    Copy-Item "src\native\python-ai" "$publishPath\src\native\python-ai" -Recurse -Force
    Copy-Item "README.md" $publishPath -Force
    Copy-Item "LICENSE" $publishPath -Force -ErrorAction SilentlyContinue
    
    Write-Info "تم نشر التطبيق بنجاح في: $publishPath / Application published successfully to: $publishPath"
    return $true
}

function Create-Package {
    Write-Header "إنشاء الحزمة / Creating Package"
    
    $packagePath = "$OutputPath\package"
    $zipPath = "$OutputPath\ELashraffyEditor-v1.0.0-win-$Platform.zip"
    
    if (Test-Path "$OutputPath\publish") {
        Write-Info "إنشاء حزمة ZIP... / Creating ZIP package..."
        
        if (Test-Path $zipPath) {
            Remove-Item $zipPath -Force
        }
        
        Compress-Archive -Path "$OutputPath\publish\*" -DestinationPath $zipPath -CompressionLevel Optimal
        
        Write-Info "تم إنشاء الحزمة: $zipPath / Package created: $zipPath"
        
        # إظهار معلومات الحزمة
        # Show package info
        $zipInfo = Get-Item $zipPath
        Write-Info "حجم الحزمة: $([math]::Round($zipInfo.Length / 1MB, 2)) MB / Package size: $([math]::Round($zipInfo.Length / 1MB, 2)) MB"
        
        return $true
    }
    else {
        Write-Error "مجلد النشر غير موجود / Publish directory not found"
        return $false
    }
}

function Show-Summary {
    param([hashtable]$Results)
    
    Write-Header "ملخص البناء / Build Summary"
    
    foreach ($step in $Results.Keys) {
        $status = if ($Results[$step]) { "✓ نجح / Success" } else { "✗ فشل / Failed" }
        $color = if ($Results[$step]) { $InfoColor } else { $ErrorColor }
        Write-Host "$step : $status" -ForegroundColor $color
    }
    
    $overallSuccess = ($Results.Values | Where-Object { $_ -eq $false }).Count -eq 0
    $overallStatus = if ($overallSuccess) { "نجح البناء / Build Successful" } else { "فشل البناء / Build Failed" }
    $overallColor = if ($overallSuccess) { $InfoColor } else { $ErrorColor }
    
    Write-Host "`n$overallStatus" -ForegroundColor $overallColor
}

# البدء في عملية البناء
# Start build process
Write-Header "محرر الأشرافي - سكريبت البناء / ELashrafy Editor - Build Script"
Write-Info "التكوين: $Configuration | المنصة: $Platform / Configuration: $Configuration | Platform: $Platform"

$results = @{}

try {
    # التنظيف
    # Clean
    if ($Clean) {
        Clean-Solution
    }
    
    # بناء Rust
    # Build Rust
    if ($BuildRust) {
        $results["Rust Build"] = Build-RustLibrary
    }
    
    # تثبيت Python
    # Install Python
    if ($InstallPython) {
        $results["Python Dependencies"] = Install-PythonDependencies
    }
    
    # استعادة الحزم
    # Restore packages
    $results["Package Restore"] = Restore-Packages
    
    # البناء
    # Build
    if ($results["Package Restore"]) {
        $results["Build"] = Build-Solution
    }
    
    # الاختبارات
    # Tests
    if ($Test -and $results["Build"]) {
        $results["Tests"] = Run-Tests
    }
    
    # النشر
    # Publish
    if ($Publish -and $results["Build"]) {
        $results["Publish"] = Publish-Application
    }
    
    # التعبئة
    # Package
    if ($Package -and $results["Publish"]) {
        $results["Package"] = Create-Package
    }
}
catch {
    Write-Error "خطأ غير متوقع: $($_.Exception.Message) / Unexpected error: $($_.Exception.Message)"
    $results["Overall"] = $false
}
finally {
    Show-Summary $results
}

# إنهاء السكريبت
# Exit script
$exitCode = if (($results.Values | Where-Object { $_ -eq $false }).Count -eq 0) { 0 } else { 1 }
exit $exitCode
