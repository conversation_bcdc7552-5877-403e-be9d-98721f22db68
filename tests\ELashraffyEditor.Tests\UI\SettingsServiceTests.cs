using ELashraffyEditor.UI.Services;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace ELashraffyEditor.Tests.UI;

/// <summary>
/// اختبارات خدمة الإعدادات
/// Settings service tests
/// </summary>
public class SettingsServiceTests : IDisposable
{
    private readonly Mock<ILogger<SettingsService>> _mockLogger;
    private readonly SettingsService _settingsService;
    private readonly string _testSettingsPath;

    public SettingsServiceTests()
    {
        _mockLogger = new Mock<ILogger<SettingsService>>();
        
        // إنشاء مجلد اختبار مؤقت
        // Create temporary test directory
        var testDir = Path.Combine(Path.GetTempPath(), "ELashraffyEditor_Settings_Tests", Guid.NewGuid().ToString());
        Directory.CreateDirectory(testDir);
        _testSettingsPath = Path.Combine(testDir, "test_settings.json");
        
        // إنشاء خدمة الإعدادات مع مسار اختبار
        // Create settings service with test path
        _settingsService = new TestableSettingsService(_mockLogger.Object, _testSettingsPath);
    }

    [Fact]
    public void LoadSettings_FirstTime_ShouldReturnDefaultSettings()
    {
        // Act
        var settings = _settingsService.LoadSettings();

        // Assert
        settings.Should().NotBeNull();
        settings.Should().ContainKey("IsDarkTheme");
        settings.Should().ContainKey("Language");
        settings["IsDarkTheme"].Should().Be(true);
        settings["Language"].Should().Be("ar-SA");
    }

    [Fact]
    public void SetSetting_ValidKeyValue_ShouldUpdateSetting()
    {
        // Arrange
        var key = "TestKey";
        var value = "TestValue";

        // Act
        _settingsService.SetSetting(key, value);
        var result = _settingsService.GetSetting<string>(key);

        // Assert
        result.Should().Be(value);
    }

    [Fact]
    public void GetSetting_ExistingKey_ShouldReturnValue()
    {
        // Arrange
        var key = "IsDarkTheme";
        var expectedValue = true;

        // Act
        var result = _settingsService.GetSetting<bool>(key);

        // Assert
        result.Should().Be(expectedValue);
    }

    [Fact]
    public void GetSetting_NonExistingKey_ShouldReturnDefaultValue()
    {
        // Arrange
        var key = "NonExistingKey";
        var defaultValue = "DefaultValue";

        // Act
        var result = _settingsService.GetSetting(key, defaultValue);

        // Assert
        result.Should().Be(defaultValue);
    }

    [Fact]
    public void HasSetting_ExistingKey_ShouldReturnTrue()
    {
        // Arrange
        var key = "IsDarkTheme";

        // Act
        var result = _settingsService.HasSetting(key);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void HasSetting_NonExistingKey_ShouldReturnFalse()
    {
        // Arrange
        var key = "NonExistingKey";

        // Act
        var result = _settingsService.HasSetting(key);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void RemoveSetting_ExistingKey_ShouldRemoveSetting()
    {
        // Arrange
        var key = "TestKey";
        _settingsService.SetSetting(key, "TestValue");
        _settingsService.HasSetting(key).Should().BeTrue();

        // Act
        _settingsService.RemoveSetting(key);

        // Assert
        _settingsService.HasSetting(key).Should().BeFalse();
    }

    [Fact]
    public void SaveSettings_ValidSettings_ShouldPersistToFile()
    {
        // Arrange
        var key = "TestPersistence";
        var value = "PersistentValue";
        _settingsService.SetSetting(key, value);

        // Act
        _settingsService.SaveSettings();

        // Assert
        File.Exists(_testSettingsPath).Should().BeTrue();
        
        // إنشاء خدمة جديدة لاختبار التحميل
        // Create new service to test loading
        var newService = new TestableSettingsService(_mockLogger.Object, _testSettingsPath);
        var loadedValue = newService.GetSetting<string>(key);
        loadedValue.Should().Be(value);
    }

    [Fact]
    public void ResetToDefaults_ShouldRestoreDefaultValues()
    {
        // Arrange
        _settingsService.SetSetting("IsDarkTheme", false);
        _settingsService.SetSetting("Language", "en-US");
        
        // Act
        _settingsService.ResetToDefaults();

        // Assert
        _settingsService.GetSetting<bool>("IsDarkTheme").Should().BeTrue();
        _settingsService.GetSetting<string>("Language").Should().Be("ar-SA");
    }

    [Theory]
    [InlineData("StringValue", "TestString")]
    [InlineData(42, 42)]
    [InlineData(3.14, 3.14)]
    [InlineData(true, true)]
    [InlineData(false, false)]
    public void SetAndGetSetting_VariousTypes_ShouldWorkCorrectly<T>(T value, T expected)
    {
        // Arrange
        var key = $"TestKey_{typeof(T).Name}";

        // Act
        _settingsService.SetSetting(key, value);
        var result = _settingsService.GetSetting<T>(key);

        // Assert
        result.Should().Be(expected);
    }

    [Fact]
    public void GetSettingsFilePath_ShouldReturnCorrectPath()
    {
        // Act
        var path = _settingsService.GetSettingsFilePath();

        // Assert
        path.Should().Be(_testSettingsPath);
    }

    [Fact]
    public void SaveSettings_WithDictionary_ShouldUpdateAllSettings()
    {
        // Arrange
        var newSettings = new Dictionary<string, object>
        {
            { "NewKey1", "NewValue1" },
            { "NewKey2", 123 },
            { "NewKey3", true }
        };

        // Act
        _settingsService.SaveSettings(newSettings);

        // Assert
        _settingsService.GetSetting<string>("NewKey1").Should().Be("NewValue1");
        _settingsService.GetSetting<int>("NewKey2").Should().Be(123);
        _settingsService.GetSetting<bool>("NewKey3").Should().BeTrue();
    }

    public void Dispose()
    {
        // تنظيف ملفات الاختبار
        // Cleanup test files
        try
        {
            if (File.Exists(_testSettingsPath))
                File.Delete(_testSettingsPath);
            
            var testDir = Path.GetDirectoryName(_testSettingsPath);
            if (Directory.Exists(testDir))
                Directory.Delete(testDir, true);
        }
        catch
        {
            // تجاهل أخطاء التنظيف
            // Ignore cleanup errors
        }
    }
}

/// <summary>
/// فئة قابلة للاختبار من SettingsService
/// Testable version of SettingsService
/// </summary>
public class TestableSettingsService : SettingsService
{
    private readonly string _customSettingsPath;

    public TestableSettingsService(ILogger<SettingsService> logger, string settingsPath) 
        : base(logger)
    {
        _customSettingsPath = settingsPath;
    }

    public new string GetSettingsFilePath()
    {
        return _customSettingsPath;
    }
}
