using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace ELashraffyEditor.Core.Models;

/// <summary>
/// نموذج عنصر الصورة في المكتبة
/// Photo item model for library
/// </summary>
public class PhotoItem : INotifyPropertyChanged
{
    private string _filePath = string.Empty;
    private string _fileName = string.Empty;
    private DateTime _dateCreated;
    private DateTime _dateModified;
    private long _fileSize;
    private int _width;
    private int _height;
    private string _format = string.Empty;
    private bool _isSelected;
    private bool _isRaw;
    private ImageAdjustment _adjustments = new();
    private byte[]? _thumbnailData;
    private string _rating = string.Empty;
    private List<string> _tags = new();

    /// <summary>
    /// مسار الملف الكامل
    /// Full file path
    /// </summary>
    public string FilePath
    {
        get => _filePath;
        set => SetProperty(ref _filePath, value);
    }

    /// <summary>
    /// اسم الملف
    /// File name
    /// </summary>
    public string FileName
    {
        get => _fileName;
        set => SetProperty(ref _fileName, value);
    }

    /// <summary>
    /// تاريخ الإنشاء
    /// Creation date
    /// </summary>
    public DateTime DateCreated
    {
        get => _dateCreated;
        set => SetProperty(ref _dateCreated, value);
    }

    /// <summary>
    /// تاريخ التعديل
    /// Modification date
    /// </summary>
    public DateTime DateModified
    {
        get => _dateModified;
        set => SetProperty(ref _dateModified, value);
    }

    /// <summary>
    /// حجم الملف بالبايت
    /// File size in bytes
    /// </summary>
    public long FileSize
    {
        get => _fileSize;
        set => SetProperty(ref _fileSize, value);
    }

    /// <summary>
    /// عرض الصورة بالبكسل
    /// Image width in pixels
    /// </summary>
    public int Width
    {
        get => _width;
        set => SetProperty(ref _width, value);
    }

    /// <summary>
    /// ارتفاع الصورة بالبكسل
    /// Image height in pixels
    /// </summary>
    public int Height
    {
        get => _height;
        set => SetProperty(ref _height, value);
    }

    /// <summary>
    /// تنسيق الملف
    /// File format
    /// </summary>
    public string Format
    {
        get => _format;
        set => SetProperty(ref _format, value);
    }

    /// <summary>
    /// هل الصورة محددة
    /// Is image selected
    /// </summary>
    public bool IsSelected
    {
        get => _isSelected;
        set => SetProperty(ref _isSelected, value);
    }

    /// <summary>
    /// هل الملف من نوع RAW
    /// Is RAW file
    /// </summary>
    public bool IsRaw
    {
        get => _isRaw;
        set => SetProperty(ref _isRaw, value);
    }

    /// <summary>
    /// تعديلات الصورة
    /// Image adjustments
    /// </summary>
    public ImageAdjustment Adjustments
    {
        get => _adjustments;
        set => SetProperty(ref _adjustments, value);
    }

    /// <summary>
    /// بيانات الصورة المصغرة
    /// Thumbnail data
    /// </summary>
    public byte[]? ThumbnailData
    {
        get => _thumbnailData;
        set => SetProperty(ref _thumbnailData, value);
    }

    /// <summary>
    /// تقييم الصورة (1-5 نجوم)
    /// Image rating (1-5 stars)
    /// </summary>
    public string Rating
    {
        get => _rating;
        set => SetProperty(ref _rating, value);
    }

    /// <summary>
    /// علامات الصورة
    /// Image tags
    /// </summary>
    public List<string> Tags
    {
        get => _tags;
        set => SetProperty(ref _tags, value);
    }

    /// <summary>
    /// حجم الملف المنسق
    /// Formatted file size
    /// </summary>
    public string FormattedFileSize
    {
        get
        {
            if (FileSize < 1024) return $"{FileSize} B";
            if (FileSize < 1024 * 1024) return $"{FileSize / 1024.0:F1} KB";
            if (FileSize < 1024 * 1024 * 1024) return $"{FileSize / (1024.0 * 1024.0):F1} MB";
            return $"{FileSize / (1024.0 * 1024.0 * 1024.0):F1} GB";
        }
    }

    /// <summary>
    /// أبعاد الصورة المنسقة
    /// Formatted image dimensions
    /// </summary>
    public string FormattedDimensions => $"{Width} × {Height}";

    /// <summary>
    /// نسبة العرض إلى الارتفاع
    /// Aspect ratio
    /// </summary>
    public double AspectRatio => Height > 0 ? (double)Width / Height : 1.0;

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }

    protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
    {
        if (EqualityComparer<T>.Default.Equals(field, value)) return false;
        field = value;
        OnPropertyChanged(propertyName);
        return true;
    }
}
