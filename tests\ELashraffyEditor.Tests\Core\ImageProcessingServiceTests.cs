using ELashraffyEditor.Core.Models;
using ELashraffyEditor.UI.Services;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.PixelFormats;
using Xunit;

namespace ELashraffyEditor.Tests.Core;

/// <summary>
/// اختبارات خدمة معالجة الصور
/// Image processing service tests
/// </summary>
public class ImageProcessingServiceTests : IDisposable
{
    private readonly Mock<ILogger<ImageProcessingService>> _mockLogger;
    private readonly ImageProcessingService _imageProcessingService;
    private readonly string _testImagePath;
    private readonly string _testOutputPath;

    public ImageProcessingServiceTests()
    {
        _mockLogger = new Mock<ILogger<ImageProcessingService>>();
        _imageProcessingService = new ImageProcessingService(_mockLogger.Object);
        
        // إنشاء مجلد الاختبار
        // Create test directory
        var testDir = Path.Combine(Path.GetTempPath(), "ELashraffyEditor_Tests");
        Directory.CreateDirectory(testDir);
        
        _testImagePath = Path.Combine(testDir, "test_image.jpg");
        _testOutputPath = Path.Combine(testDir, "test_output.jpg");
        
        // إنشاء صورة اختبار
        // Create test image
        CreateTestImage(_testImagePath);
    }

    [Fact]
    public async Task LoadImageAsync_ValidPath_ShouldReturnImage()
    {
        // Act
        var result = await _imageProcessingService.LoadImageAsync(_testImagePath);

        // Assert
        result.Should().NotBeNull();
        result!.Width.Should().Be(100);
        result.Height.Should().Be(100);
    }

    [Fact]
    public async Task LoadImageAsync_InvalidPath_ShouldReturnNull()
    {
        // Arrange
        var invalidPath = "non_existent_file.jpg";

        // Act
        var result = await _imageProcessingService.LoadImageAsync(invalidPath);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task SaveImageAsync_ValidImage_ShouldReturnTrue()
    {
        // Arrange
        using var image = await _imageProcessingService.LoadImageAsync(_testImagePath);
        image.Should().NotBeNull();

        // Act
        var result = await _imageProcessingService.SaveImageAsync(image!, _testOutputPath);

        // Assert
        result.Should().BeTrue();
        File.Exists(_testOutputPath).Should().BeTrue();
    }

    [Fact]
    public async Task GenerateThumbnailAsync_ValidImage_ShouldReturnThumbnailData()
    {
        // Act
        var result = await _imageProcessingService.GenerateThumbnailAsync(_testImagePath, 50, 50);

        // Assert
        result.Should().NotBeNull();
        result!.Length.Should().BeGreaterThan(0);
    }

    [Fact]
    public async Task ApplyAdjustmentsAsync_ValidAdjustments_ShouldReturnModifiedImage()
    {
        // Arrange
        using var originalImage = await _imageProcessingService.LoadImageAsync(_testImagePath);
        originalImage.Should().NotBeNull();

        var adjustments = new ImageAdjustment
        {
            Brightness = 10,
            Contrast = 15,
            Saturation = 5
        };

        // Act
        using var result = await _imageProcessingService.ApplyAdjustmentsAsync(originalImage!, adjustments);

        // Assert
        result.Should().NotBeNull();
        result!.Width.Should().Be(originalImage!.Width);
        result.Height.Should().Be(originalImage.Height);
    }

    [Fact]
    public async Task GetImageInfoAsync_ValidImage_ShouldReturnPhotoItem()
    {
        // Act
        var result = await _imageProcessingService.GetImageInfoAsync(_testImagePath);

        // Assert
        result.Should().NotBeNull();
        result!.FileName.Should().Be("test_image.jpg");
        result.Width.Should().Be(100);
        result.Height.Should().Be(100);
        result.Format.Should().Be("JPG");
    }

    [Fact]
    public async Task GenerateHistogramAsync_ValidImage_ShouldReturnHistogramData()
    {
        // Arrange
        using var image = await _imageProcessingService.LoadImageAsync(_testImagePath);
        image.Should().NotBeNull();

        // Act
        var result = await _imageProcessingService.GenerateHistogramAsync(image!);

        // Assert
        result.Should().NotBeNull();
        result.Length.Should().Be(3); // RGB channels
        result[0].Length.Should().Be(256); // 256 values per channel
        result[1].Length.Should().Be(256);
        result[2].Length.Should().Be(256);
    }

    [Fact]
    public async Task AutoEnhanceAsync_ValidImage_ShouldReturnAdjustments()
    {
        // Arrange
        using var image = await _imageProcessingService.LoadImageAsync(_testImagePath);
        image.Should().NotBeNull();

        // Act
        var result = await _imageProcessingService.AutoEnhanceAsync(image!);

        // Assert
        result.Should().NotBeNull();
        // التحقق من أن التعديلات في النطاق المتوقع
        // Verify adjustments are within expected range
        result.Brightness.Should().BeInRange(-100, 100);
        result.Contrast.Should().BeInRange(-100, 100);
        result.Saturation.Should().BeInRange(-100, 100);
    }

    [Fact]
    public async Task ResizeImageAsync_ValidDimensions_ShouldReturnResizedImage()
    {
        // Arrange
        using var originalImage = await _imageProcessingService.LoadImageAsync(_testImagePath);
        originalImage.Should().NotBeNull();

        // Act
        using var result = await _imageProcessingService.ResizeImageAsync(originalImage!, 50, 50);

        // Assert
        result.Should().NotBeNull();
        result!.Width.Should().Be(50);
        result.Height.Should().Be(50);
    }

    [Fact]
    public async Task RotateImageAsync_ValidAngle_ShouldReturnRotatedImage()
    {
        // Arrange
        using var originalImage = await _imageProcessingService.LoadImageAsync(_testImagePath);
        originalImage.Should().NotBeNull();

        // Act
        using var result = await _imageProcessingService.RotateImageAsync(originalImage!, 90);

        // Assert
        result.Should().NotBeNull();
        // بعد دوران 90 درجة، العرض والارتفاع يجب أن يتبادلا
        // After 90-degree rotation, width and height should swap
        result!.Width.Should().Be(originalImage!.Height);
        result.Height.Should().Be(originalImage.Width);
    }

    [Fact]
    public void GetSupportedFormats_ShouldReturnExpectedFormats()
    {
        // Act
        var result = _imageProcessingService.GetSupportedFormats();

        // Assert
        result.Should().NotBeEmpty();
        result.Should().Contain(".jpg");
        result.Should().Contain(".png");
        result.Should().Contain(".tiff");
    }

    [Theory]
    [InlineData(".jpg", true)]
    [InlineData(".jpeg", true)]
    [InlineData(".png", true)]
    [InlineData(".tiff", true)]
    [InlineData(".bmp", true)]
    [InlineData(".xyz", false)]
    [InlineData(".txt", false)]
    public void IsFormatSupported_VariousExtensions_ShouldReturnExpectedResult(string extension, bool expected)
    {
        // Act
        var result = _imageProcessingService.IsFormatSupported(extension);

        // Assert
        result.Should().Be(expected);
    }

    private void CreateTestImage(string path)
    {
        // إنشاء صورة اختبار بسيطة 100x100 بكسل
        // Create simple 100x100 pixel test image
        using var image = new Image<Rgba32>(100, 100);
        
        // ملء الصورة بلون أزرق
        // Fill image with blue color
        for (int y = 0; y < image.Height; y++)
        {
            for (int x = 0; x < image.Width; x++)
            {
                image[x, y] = new Rgba32(0, 100, 200, 255);
            }
        }
        
        image.SaveAsJpeg(path);
    }

    public void Dispose()
    {
        // تنظيف ملفات الاختبار
        // Cleanup test files
        try
        {
            if (File.Exists(_testImagePath))
                File.Delete(_testImagePath);
            
            if (File.Exists(_testOutputPath))
                File.Delete(_testOutputPath);
        }
        catch
        {
            // تجاهل أخطاء التنظيف
            // Ignore cleanup errors
        }
    }
}
