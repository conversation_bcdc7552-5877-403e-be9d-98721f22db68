using ELashraffyEditor.Core.Models;
using Microsoft.Extensions.Logging;
using System.Diagnostics;
using System.Text.Json;

namespace ELashraffyEditor.Scripting.Python;

/// <summary>
/// خدمة الذكاء الاصطناعي بـ Python
/// Python AI service
/// </summary>
public class PythonAIService : IPythonAIService
{
    private readonly ILogger<PythonAIService> _logger;
    private readonly string _pythonScriptPath;
    private readonly string _pythonExecutable;
    private bool _environmentChecked = false;
    private bool _environmentReady = false;

    public PythonAIService(ILogger<PythonAIService> logger)
    {
        _logger = logger;
        
        // تحديد مسارات Python
        // Determine Python paths
        _pythonScriptPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "src", "native", "python-ai", "image_ai.py");
        _pythonExecutable = FindPythonExecutable();
        
        _logger.LogInformation("PythonAIService initialized with Python: {PythonPath}", _pythonExecutable);
    }

    public async Task<AIEnhancementResult> AutoEnhanceSmartAsync(string imagePath)
    {
        try
        {
            if (!await EnsureEnvironmentReadyAsync())
            {
                return new AIEnhancementResult
                {
                    Success = false,
                    ErrorMessage = "Python environment not ready"
                };
            }

            var result = await ExecutePythonScriptAsync("auto_enhance", imagePath);
            if (result == null)
            {
                return new AIEnhancementResult { Success = false, ErrorMessage = "No result from Python script" };
            }

            if (!result.Value.TryGetProperty("success", out var successProp) || !successProp.GetBoolean())
            {
                var errorMsg = result.HasValue && result.Value.TryGetProperty("error", out var errorProp) ? errorProp.GetString() : "Unknown error";
                return new AIEnhancementResult { Success = false, ErrorMessage = errorMsg };
            }

            var resultValue = result.Value;
            var adjustments = new ImageAdjustment();
            if (resultValue.TryGetProperty("adjustments", out var adjustmentsProp))
            {
                ParseAdjustments(adjustmentsProp, adjustments);
            }

            var analysis = new Dictionary<string, object>();
            if (resultValue.TryGetProperty("analysis", out var analysisProp))
            {
                ParseAnalysis(analysisProp, analysis);
            }

            return new AIEnhancementResult
            {
                Success = true,
                Adjustments = adjustments,
                Analysis = analysis,
                ConfidenceScore = 0.85f
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to auto enhance image: {ImagePath}", imagePath);
            return new AIEnhancementResult
            {
                Success = false,
                ErrorMessage = $"Auto enhancement failed: {ex.Message}"
            };
        }
    }

    public async Task<FaceDetectionResult> DetectFacesAsync(string imagePath)
    {
        try
        {
            if (!await EnsureEnvironmentReadyAsync())
            {
                return new FaceDetectionResult
                {
                    Success = false,
                    ErrorMessage = "Python environment not ready"
                };
            }

            var result = await ExecutePythonScriptAsync("detect_faces", imagePath);
            if (result == null || !result.Value.TryGetProperty("success", out var successProp) || !successProp.GetBoolean())
            {
                var errorMsg = result.HasValue && result.Value.TryGetProperty("error", out var errorProp) ? errorProp.GetString() : "Unknown error";
                return new FaceDetectionResult { Success = false, ErrorMessage = errorMsg };
            }

            var resultValue = result.Value;
            var faces = new List<DetectedFace>();
            if (resultValue.TryGetProperty("faces", out var facesProp) && facesProp.ValueKind == JsonValueKind.Array)
            {
                foreach (var faceElement in facesProp.EnumerateArray())
                {
                    var face = new DetectedFace
                    {
                        X = faceElement.TryGetProperty("x", out var xProp) ? xProp.GetInt32() : 0,
                        Y = faceElement.TryGetProperty("y", out var yProp) ? yProp.GetInt32() : 0,
                        Width = faceElement.TryGetProperty("width", out var wProp) ? wProp.GetInt32() : 0,
                        Height = faceElement.TryGetProperty("height", out var hProp) ? hProp.GetInt32() : 0,
                        Confidence = faceElement.TryGetProperty("confidence", out var confProp) ? (float)confProp.GetDouble() : 0.8f
                    };
                    faces.Add(face);
                }
            }

            var faceCount = resultValue.TryGetProperty("count", out var countProp) ? countProp.GetInt32() : faces.Count;

            return new FaceDetectionResult
            {
                Success = true,
                Faces = faces,
                FaceCount = faceCount
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to detect faces: {ImagePath}", imagePath);
            return new FaceDetectionResult
            {
                Success = false,
                ErrorMessage = $"Face detection failed: {ex.Message}"
            };
        }
    }

    public async Task<AIEnhancementResult> EnhancePortraitAsync(string imagePath)
    {
        try
        {
            if (!await EnsureEnvironmentReadyAsync())
            {
                return new AIEnhancementResult
                {
                    Success = false,
                    ErrorMessage = "Python environment not ready"
                };
            }

            var result = await ExecutePythonScriptAsync("enhance_portrait", imagePath);
            if (result == null || !result.Value.TryGetProperty("success", out var successProp) || !successProp.GetBoolean())
            {
                var errorMsg = result.HasValue && result.Value.TryGetProperty("error", out var errorProp) ? errorProp.GetString() : "Unknown error";
                return new AIEnhancementResult { Success = false, ErrorMessage = errorMsg };
            }

            var resultValue = result.Value;
            var adjustments = new ImageAdjustment();
            if (resultValue.TryGetProperty("adjustments", out var adjustmentsProp))
            {
                ParseAdjustments(adjustmentsProp, adjustments);
            }

            var imageType = resultValue.TryGetProperty("type", out var typeProp) ? typeProp.GetString() : "portrait";

            return new AIEnhancementResult
            {
                Success = true,
                Adjustments = adjustments,
                ImageType = imageType,
                ConfidenceScore = 0.9f
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to enhance portrait: {ImagePath}", imagePath);
            return new AIEnhancementResult
            {
                Success = false,
                ErrorMessage = $"Portrait enhancement failed: {ex.Message}"
            };
        }
    }

    public async Task<ColorCorrectionResult> AutoColorCorrectionAsync(string imagePath)
    {
        try
        {
            if (!await EnsureEnvironmentReadyAsync())
            {
                return new ColorCorrectionResult
                {
                    Success = false,
                    ErrorMessage = "Python environment not ready"
                };
            }

            var result = await ExecutePythonScriptAsync("color_correction", imagePath);
            if (result == null || !result.Value.TryGetProperty("success", out var successProp) || !successProp.GetBoolean())
            {
                var errorMsg = result.HasValue && result.Value.TryGetProperty("error", out var errorProp) ? errorProp.GetString() : "Unknown error";
                return new ColorCorrectionResult { Success = false, ErrorMessage = errorMsg };
            }

            var resultValue = result.Value;
            var corrections = new ColorCorrectionResult { Success = true };

            if (resultValue.TryGetProperty("corrections", out var correctionsProp))
            {
                corrections.TemperatureShift = correctionsProp.TryGetProperty("temperature", out var tempProp) ? (float)tempProp.GetDouble() : 0f;
                corrections.TintShift = correctionsProp.TryGetProperty("tint", out var tintProp) ? (float)tintProp.GetDouble() : 0f;
                corrections.WhiteBalanceCorrection = correctionsProp.TryGetProperty("white_balance", out var wbProp) && wbProp.GetBoolean();
            }

            if (resultValue.TryGetProperty("analysis", out var analysisProp))
            {
                corrections.ColorCast = analysisProp.TryGetProperty("color_cast", out var castProp) ? castProp.GetString() : "neutral";
            }

            return corrections;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to auto correct colors: {ImagePath}", imagePath);
            return new ColorCorrectionResult
            {
                Success = false,
                ErrorMessage = $"Color correction failed: {ex.Message}"
            };
        }
    }

    public async Task<NoiseReductionResult> ReduceNoiseAIAsync(string imagePath, float strength = 0.5f)
    {
        try
        {
            if (!await EnsureEnvironmentReadyAsync())
            {
                return new NoiseReductionResult
                {
                    Success = false,
                    ErrorMessage = "Python environment not ready"
                };
            }

            var result = await ExecutePythonScriptAsync("noise_reduction", imagePath, strength.ToString());
            if (result == null || !result.Value.TryGetProperty("success", out var successProp) || !successProp.GetBoolean())
            {
                var errorMsg = result.HasValue && result.Value.TryGetProperty("error", out var errorProp) ? errorProp.GetString() : "Unknown error";
                return new NoiseReductionResult { Success = false, ErrorMessage = errorMsg };
            }

            var noiseResult = new NoiseReductionResult { Success = true };
            var resultValue = result.Value;

            noiseResult.OutputPath = resultValue.TryGetProperty("output_path", out var pathProp) ? pathProp.GetString() : null;
            noiseResult.StrengthUsed = resultValue.TryGetProperty("strength_used", out var strengthProp) ? (float)strengthProp.GetDouble() : strength;

            if (resultValue.TryGetProperty("noise_analysis", out var analysisProp))
            {
                noiseResult.NoiseType = analysisProp.TryGetProperty("type", out var typeProp) ? typeProp.GetString() : "unknown";
                noiseResult.NoiseLevel = analysisProp.TryGetProperty("level", out var levelProp) ? (float)levelProp.GetDouble() : 0f;
                noiseResult.NoiseSeverity = analysisProp.TryGetProperty("severity", out var severityProp) ? severityProp.GetString() : "unknown";
            }

            return noiseResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to reduce noise: {ImagePath}", imagePath);
            return new NoiseReductionResult
            {
                Success = false,
                ErrorMessage = $"Noise reduction failed: {ex.Message}"
            };
        }
    }

    public async Task<ImageQualityAnalysis> AnalyzeImageQualityAsync(string imagePath)
    {
        // تنفيذ مبسط - يمكن توسيعه لاحقاً
        // Simplified implementation - can be expanded later
        await Task.Delay(100);
        
        return new ImageQualityAnalysis
        {
            Success = true,
            OverallQuality = 0.8f,
            Sharpness = 0.75f,
            Brightness = 0.7f,
            Contrast = 0.8f,
            ColorBalance = 0.85f,
            NoiseLevel = 0.2f
        };
    }

    public async Task<ImprovementSuggestions> SuggestImprovementsAsync(string imagePath)
    {
        // تنفيذ مبسط - يمكن توسيعه لاحقاً
        // Simplified implementation - can be expanded later
        await Task.Delay(100);
        
        return new ImprovementSuggestions
        {
            Success = true,
            Suggestions = new List<Suggestion>
            {
                new Suggestion
                {
                    Type = "brightness",
                    Description = "Increase brightness slightly",
                    DescriptionArabic = "زيادة السطوع قليلاً",
                    Priority = 1,
                    Impact = 0.3f
                }
            }
        };
    }

    public async Task<ImageClassificationResult> ClassifyImageAsync(string imagePath)
    {
        // تنفيذ مبسط - يمكن توسيعه لاحقاً
        // Simplified implementation - can be expanded later
        await Task.Delay(100);
        
        return new ImageClassificationResult
        {
            Success = true,
            ImageType = "portrait",
            Confidence = 0.85f,
            Classifications = new List<Classification>
            {
                new Classification
                {
                    Category = "type",
                    Label = "portrait",
                    Confidence = 0.85f
                }
            }
        };
    }

    public async Task<bool> CheckPythonEnvironmentAsync()
    {
        try
        {
            var process = new Process
            {
                StartInfo = new ProcessStartInfo
                {
                    FileName = _pythonExecutable,
                    Arguments = "--version",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                }
            };

            process.Start();
            await process.WaitForExitAsync();

            _environmentReady = process.ExitCode == 0;
            _environmentChecked = true;

            _logger.LogInformation("Python environment check: {Status}", _environmentReady ? "Ready" : "Not Ready");
            return _environmentReady;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to check Python environment");
            _environmentReady = false;
            _environmentChecked = true;
            return false;
        }
    }

    public async Task<bool> InstallRequiredLibrariesAsync()
    {
        try
        {
            var requirementsPath = Path.Combine(Path.GetDirectoryName(_pythonScriptPath)!, "requirements.txt");
            if (!File.Exists(requirementsPath))
            {
                _logger.LogWarning("Requirements file not found: {Path}", requirementsPath);
                return false;
            }

            var process = new Process
            {
                StartInfo = new ProcessStartInfo
                {
                    FileName = _pythonExecutable,
                    Arguments = $"-m pip install -r \"{requirementsPath}\"",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                }
            };

            process.Start();
            await process.WaitForExitAsync();

            var success = process.ExitCode == 0;
            _logger.LogInformation("Python libraries installation: {Status}", success ? "Success" : "Failed");
            
            return success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to install Python libraries");
            return false;
        }
    }

    public async Task<PythonEnvironmentInfo> GetEnvironmentInfoAsync()
    {
        var info = new PythonEnvironmentInfo();
        
        try
        {
            info.PythonAvailable = await CheckPythonEnvironmentAsync();
            info.PythonPath = _pythonExecutable;
            
            if (info.PythonAvailable)
            {
                // الحصول على إصدار Python
                // Get Python version
                var versionProcess = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = _pythonExecutable,
                        Arguments = "--version",
                        UseShellExecute = false,
                        RedirectStandardOutput = true,
                        CreateNoWindow = true
                    }
                };
                
                versionProcess.Start();
                info.PythonVersion = await versionProcess.StandardOutput.ReadToEndAsync();
                await versionProcess.WaitForExitAsync();
            }
            
            info.AllRequirementsMet = info.PythonAvailable;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get Python environment info");
        }
        
        return info;
    }

    private async Task<bool> EnsureEnvironmentReadyAsync()
    {
        if (!_environmentChecked)
        {
            await CheckPythonEnvironmentAsync();
        }
        return _environmentReady;
    }

    private async Task<JsonElement?> ExecutePythonScriptAsync(string operation, string imagePath, params string[] additionalArgs)
    {
        try
        {
            var args = new List<string> { $"\"{_pythonScriptPath}\"", operation, $"\"{imagePath}\"" };
            args.AddRange(additionalArgs);

            var process = new Process
            {
                StartInfo = new ProcessStartInfo
                {
                    FileName = _pythonExecutable,
                    Arguments = string.Join(" ", args),
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                }
            };

            process.Start();
            var output = await process.StandardOutput.ReadToEndAsync();
            var error = await process.StandardError.ReadToEndAsync();
            await process.WaitForExitAsync();

            if (process.ExitCode != 0)
            {
                _logger.LogError("Python script failed with exit code {ExitCode}: {Error}", process.ExitCode, error);
                return null;
            }

            if (string.IsNullOrWhiteSpace(output))
            {
                _logger.LogWarning("Python script returned empty output");
                return null;
            }

            return JsonSerializer.Deserialize<JsonElement>(output);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to execute Python script: {Operation}", operation);
            return null;
        }
    }

    private void ParseAdjustments(JsonElement adjustmentsProp, ImageAdjustment adjustments)
    {
        if (adjustmentsProp.TryGetProperty("brightness", out var brightnessProp))
            adjustments.Brightness = (float)brightnessProp.GetDouble();
        
        if (adjustmentsProp.TryGetProperty("contrast", out var contrastProp))
            adjustments.Contrast = (float)contrastProp.GetDouble();
        
        if (adjustmentsProp.TryGetProperty("saturation", out var saturationProp))
            adjustments.Saturation = (float)saturationProp.GetDouble();
        
        if (adjustmentsProp.TryGetProperty("exposure", out var exposureProp))
            adjustments.Exposure = (float)exposureProp.GetDouble();
        
        if (adjustmentsProp.TryGetProperty("shadows", out var shadowsProp))
            adjustments.Shadows = (float)shadowsProp.GetDouble();
        
        if (adjustmentsProp.TryGetProperty("highlights", out var highlightsProp))
            adjustments.Highlights = (float)highlightsProp.GetDouble();
        
        if (adjustmentsProp.TryGetProperty("clarity", out var clarityProp))
            adjustments.Clarity = (float)clarityProp.GetDouble();
        
        if (adjustmentsProp.TryGetProperty("vibrance", out var vibranceProp))
            adjustments.Vibrance = (float)vibranceProp.GetDouble();
    }

    private void ParseAnalysis(JsonElement analysisProp, Dictionary<string, object> analysis)
    {
        foreach (var property in analysisProp.EnumerateObject())
        {
            analysis[property.Name] = property.Value.ValueKind switch
            {
                JsonValueKind.String => property.Value.GetString()!,
                JsonValueKind.Number => property.Value.GetDouble(),
                JsonValueKind.True => true,
                JsonValueKind.False => false,
                _ => property.Value.ToString()
            };
        }
    }

    private string FindPythonExecutable()
    {
        // البحث عن Python في المسارات الشائعة
        // Search for Python in common paths
        var possiblePaths = new[]
        {
            "python",
            "python3",
            "py",
            @"C:\Python39\python.exe",
            @"C:\Python310\python.exe",
            @"C:\Python311\python.exe",
            @"C:\Users\<USER>\AppData\Local\Programs\Python\Python39\python.exe",
            @"C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe",
            @"C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe"
        };

        foreach (var path in possiblePaths)
        {
            try
            {
                var process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = path,
                        Arguments = "--version",
                        UseShellExecute = false,
                        RedirectStandardOutput = true,
                        RedirectStandardError = true,
                        CreateNoWindow = true
                    }
                };

                process.Start();
                process.WaitForExit(5000);

                if (process.ExitCode == 0)
                {
                    _logger.LogInformation("Found Python at: {Path}", path);
                    return path;
                }
            }
            catch
            {
                // تجاهل الأخطاء والمتابعة
                // Ignore errors and continue
            }
        }

        _logger.LogWarning("Python executable not found, using default 'python'");
        return "python";
    }
}
