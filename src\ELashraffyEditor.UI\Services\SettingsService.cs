using Microsoft.Extensions.Logging;
using System.IO;
using System.Text.Json;

namespace ELashraffyEditor.UI.Services;

/// <summary>
/// خدمة إدارة الإعدادات
/// Settings management service
/// </summary>
public class SettingsService : ISettingsService
{
    private readonly ILogger<SettingsService> _logger;
    private readonly string _settingsFilePath;
    private Dictionary<string, object> _settings;
    private readonly Dictionary<string, object> _defaultSettings;

    public SettingsService(ILogger<SettingsService> logger)
    {
        _logger = logger;
        
        // تحديد مسار ملف الإعدادات
        // Determine settings file path
        var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
        var appFolder = Path.Combine(appDataPath, "ELashraffyEditor");
        Directory.CreateDirectory(appFolder);
        _settingsFilePath = Path.Combine(appFolder, "settings.json");

        // الإعدادات الافتراضية
        // Default settings
        _defaultSettings = new Dictionary<string, object>
        {
            { "IsDarkTheme", true },
            { "Language", "ar-SA" },
            { "WindowWidth", 1400.0 },
            { "WindowHeight", 900.0 },
            { "WindowState", "Maximized" },
            { "LastOpenedFolder", Environment.GetFolderPath(Environment.SpecialFolder.MyPictures) },
            { "AutoSaveEnabled", true },
            { "AutoSaveInterval", 300 }, // 5 minutes
            { "ThumbnailSize", 200 },
            { "ShowHistogram", true },
            { "ShowMetadata", true },
            { "DefaultExportFormat", "JPEG" },
            { "DefaultExportQuality", 95 },
            { "EnableGPUAcceleration", true },
            { "MaxMemoryUsage", 4096 }, // 4GB
            { "RecentFiles", new List<string>() },
            { "CustomPresets", new List<object>() }
        };

        _settings = new Dictionary<string, object>(_defaultSettings);
        LoadSettings();
    }

    public Dictionary<string, object> LoadSettings()
    {
        try
        {
            if (File.Exists(_settingsFilePath))
            {
                var json = File.ReadAllText(_settingsFilePath);
                var loadedSettings = JsonSerializer.Deserialize<Dictionary<string, JsonElement>>(json);
                
                if (loadedSettings != null)
                {
                    _settings.Clear();
                    
                    // نسخ الإعدادات الافتراضية أولاً
                    // Copy default settings first
                    foreach (var defaultSetting in _defaultSettings)
                    {
                        _settings[defaultSetting.Key] = defaultSetting.Value;
                    }
                    
                    // تحديث بالإعدادات المحملة
                    // Update with loaded settings
                    foreach (var setting in loadedSettings)
                    {
                        _settings[setting.Key] = ConvertJsonElement(setting.Value);
                    }
                }
                
                _logger.LogInformation("Settings loaded from: {FilePath}", _settingsFilePath);
            }
            else
            {
                _logger.LogInformation("Settings file not found, using defaults");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load settings, using defaults");
            _settings = new Dictionary<string, object>(_defaultSettings);
        }

        return new Dictionary<string, object>(_settings);
    }

    public void SaveSettings()
    {
        SaveSettings(_settings);
    }

    public void SaveSettings(Dictionary<string, object> settings)
    {
        try
        {
            _settings = new Dictionary<string, object>(settings);
            
            var options = new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };
            
            var json = JsonSerializer.Serialize(_settings, options);
            File.WriteAllText(_settingsFilePath, json);
            
            _logger.LogInformation("Settings saved to: {FilePath}", _settingsFilePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save settings");
        }
    }

    public T GetSetting<T>(string key, T defaultValue = default!)
    {
        try
        {
            if (_settings.TryGetValue(key, out var value))
            {
                if (value is T directValue)
                {
                    return directValue;
                }
                
                // محاولة التحويل
                // Try conversion
                if (value is JsonElement jsonElement)
                {
                    return ConvertJsonElement<T>(jsonElement);
                }
                
                return (T)Convert.ChangeType(value, typeof(T));
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to get setting: {Key}, using default value", key);
        }
        
        return defaultValue;
    }

    public void SetSetting<T>(string key, T value)
    {
        try
        {
            _settings[key] = value!;
            _logger.LogDebug("Setting updated: {Key} = {Value}", key, value);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to set setting: {Key}", key);
        }
    }

    public void RemoveSetting(string key)
    {
        try
        {
            _settings.Remove(key);
            _logger.LogDebug("Setting removed: {Key}", key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to remove setting: {Key}", key);
        }
    }

    public bool HasSetting(string key)
    {
        return _settings.ContainsKey(key);
    }

    public void ResetToDefaults()
    {
        try
        {
            _settings = new Dictionary<string, object>(_defaultSettings);
            SaveSettings();
            _logger.LogInformation("Settings reset to defaults");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to reset settings to defaults");
        }
    }

    public string GetSettingsFilePath()
    {
        return _settingsFilePath;
    }

    private object ConvertJsonElement(JsonElement element)
    {
        return element.ValueKind switch
        {
            JsonValueKind.String => element.GetString()!,
            JsonValueKind.Number => element.TryGetInt32(out var intValue) ? intValue : element.GetDouble(),
            JsonValueKind.True => true,
            JsonValueKind.False => false,
            JsonValueKind.Array => element.EnumerateArray().Select(ConvertJsonElement).ToList(),
            JsonValueKind.Object => element.EnumerateObject().ToDictionary(p => p.Name, p => ConvertJsonElement(p.Value)),
            JsonValueKind.Null => null!,
            _ => element.ToString()
        };
    }

    private T ConvertJsonElement<T>(JsonElement element)
    {
        var targetType = typeof(T);
        
        if (targetType == typeof(string))
            return (T)(object)element.GetString()!;
        if (targetType == typeof(int))
            return (T)(object)element.GetInt32();
        if (targetType == typeof(double))
            return (T)(object)element.GetDouble();
        if (targetType == typeof(bool))
            return (T)(object)element.GetBoolean();
        if (targetType == typeof(List<string>))
            return (T)(object)element.EnumerateArray().Select(e => e.GetString()!).ToList();
        
        return JsonSerializer.Deserialize<T>(element.GetRawText())!;
    }
}
