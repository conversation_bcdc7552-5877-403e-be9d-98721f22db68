<Window x:Class="ELashraffyEditor.UI.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:viewmodels="clr-namespace:ELashraffyEditor.UI.ViewModels"
        Title="ELashrafy Editor - محرر الأشرافي"
        Height="900" Width="1400"
        MinHeight="600" MinWidth="1000"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized"
        Background="{StaticResource PrimaryBackgroundBrush}"
        TextElement.Foreground="{StaticResource PrimaryTextBrush}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="{StaticResource BodyFontSize}"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        FontFamily="{StaticResource PrimaryFontFamily}"
        FlowDirection="LeftToRight">

    <Window.Resources>
        <Style x:Key="SidebarButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignFlatButton}">
            <Setter Property="Height" Value="50"/>
            <Setter Property="Margin" Value="0,2"/>
            <Setter Property="Padding" Value="16,8"/>
            <Setter Property="HorizontalAlignment" Value="Stretch"/>
            <Setter Property="HorizontalContentAlignment" Value="Left"/>
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="{StaticResource SecondaryTextBrush}"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="{StaticResource TertiaryBackgroundBrush}"/>
                    <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <!-- Title Bar -->
            <RowDefinition Height="Auto"/>
            <!-- Main Content -->
            <RowDefinition Height="*"/>
            <!-- Status Bar -->
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Custom Title Bar -->
        <Border Grid.Row="0" Background="{StaticResource SecondaryBackgroundBrush}" Height="40">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- App Logo and Title -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center" Margin="16,0">
                    <materialDesign:PackIcon Kind="ImageEdit" Width="24" Height="24" 
                                           Foreground="{StaticResource AccentBrush}" VerticalAlignment="Center"/>
                    <TextBlock Text="ELashrafy Editor" FontWeight="SemiBold" FontSize="16" 
                             Margin="8,0,0,0" VerticalAlignment="Center"/>
                </StackPanel>

                <!-- Window Controls -->
                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <Button Style="{StaticResource MaterialDesignFlatButton}" Width="46" Height="32"
                            Click="MinimizeButton_Click" ToolTip="تصغير / Minimize">
                        <materialDesign:PackIcon Kind="WindowMinimize" Width="16" Height="16"/>
                    </Button>
                    <Button Style="{StaticResource MaterialDesignFlatButton}" Width="46" Height="32"
                            Click="MaximizeButton_Click" ToolTip="تكبير / Maximize">
                        <materialDesign:PackIcon Kind="WindowMaximize" Width="16" Height="16"/>
                    </Button>
                    <Button Style="{StaticResource MaterialDesignFlatButton}" Width="46" Height="32"
                            Click="CloseButton_Click" ToolTip="إغلاق / Close"
                            Foreground="#FFE81123">
                        <materialDesign:PackIcon Kind="Close" Width="16" Height="16"/>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main Content Area -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <!-- Left Sidebar -->
                <ColumnDefinition Width="250"/>
                <!-- Splitter -->
                <ColumnDefinition Width="Auto"/>
                <!-- Center Content -->
                <ColumnDefinition Width="*"/>
                <!-- Splitter -->
                <ColumnDefinition Width="Auto"/>
                <!-- Right Panel -->
                <ColumnDefinition Width="300"/>
            </Grid.ColumnDefinitions>

            <!-- Left Sidebar -->
            <Border Grid.Column="0" Background="{StaticResource SecondaryBackgroundBrush}" 
                    BorderBrush="{StaticResource BorderBrush}" BorderThickness="0,0,1,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Navigation Buttons -->
                    <StackPanel Grid.Row="0" Margin="8">
                        <Button x:Name="LibraryButton" Style="{StaticResource SidebarButtonStyle}" 
                                Click="NavigationButton_Click" Tag="Library">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="FolderImage" Width="20" Height="20" VerticalAlignment="Center"/>
                                <TextBlock Text="المكتبة / Library" Margin="12,0,0,0" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="EditButton" Style="{StaticResource SidebarButtonStyle}" 
                                Click="NavigationButton_Click" Tag="Edit">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="ImageEdit" Width="20" Height="20" VerticalAlignment="Center"/>
                                <TextBlock Text="التحرير / Edit" Margin="12,0,0,0" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="ExportButton" Style="{StaticResource SidebarButtonStyle}" 
                                Click="NavigationButton_Click" Tag="Export">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Export" Width="20" Height="20" VerticalAlignment="Center"/>
                                <TextBlock Text="التصدير / Export" Margin="12,0,0,0" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="SettingsButton" Style="{StaticResource SidebarButtonStyle}" 
                                Click="NavigationButton_Click" Tag="Settings">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Settings" Width="20" Height="20" VerticalAlignment="Center"/>
                                <TextBlock Text="الإعدادات / Settings" Margin="12,0,0,0" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>

                    <!-- Additional Content Area -->
                    <ContentControl Grid.Row="1" x:Name="SidebarContent" Margin="8"/>
                </Grid>
            </Border>

            <!-- Left Splitter -->
            <GridSplitter Grid.Column="1" Width="4" Background="{StaticResource BorderBrush}" 
                         HorizontalAlignment="Center" VerticalAlignment="Stretch"/>

            <!-- Center Content Area -->
            <Border Grid.Column="2" Background="{StaticResource PrimaryBackgroundBrush}">
                <ContentControl x:Name="MainContent" Margin="8">
                    <!-- Default Welcome Screen -->
                    <Grid>
                        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                            <materialDesign:PackIcon Kind="ImageMultiple" Width="128" Height="128" 
                                                   Foreground="{StaticResource SecondaryTextBrush}" 
                                                   HorizontalAlignment="Center"/>
                            <TextBlock Text="مرحباً بك في محرر الأشرافي" FontSize="24" FontWeight="SemiBold" 
                                     HorizontalAlignment="Center" Margin="0,16,0,8"/>
                            <TextBlock Text="Welcome to ELashrafy Editor" FontSize="18" 
                                     Foreground="{StaticResource SecondaryTextBrush}"
                                     HorizontalAlignment="Center" Margin="0,0,0,24"/>
                            <Button Style="{StaticResource MaterialDesignRaisedButton}" 
                                  Background="{StaticResource AccentBrush}"
                                  Content="استيراد الصور / Import Photos" 
                                  Padding="24,12" FontSize="16"
                                  Click="ImportPhotos_Click"/>
                        </StackPanel>
                    </Grid>
                </ContentControl>
            </Border>

            <!-- Right Splitter -->
            <GridSplitter Grid.Column="3" Width="4" Background="{StaticResource BorderBrush}" 
                         HorizontalAlignment="Center" VerticalAlignment="Stretch"/>

            <!-- Right Panel -->
            <Border Grid.Column="4" Background="{StaticResource SecondaryBackgroundBrush}" 
                    BorderBrush="{StaticResource BorderBrush}" BorderThickness="1,0,0,0">
                <ContentControl x:Name="RightPanelContent" Margin="8">
                    <!-- Default Right Panel Content -->
                    <StackPanel>
                        <TextBlock Text="لوحة الأدوات / Tools Panel" FontWeight="SemiBold" 
                                 Margin="0,0,0,16" HorizontalAlignment="Center"/>
                        <TextBlock Text="حدد صورة لبدء التحرير" 
                                 Foreground="{StaticResource SecondaryTextBrush}"
                                 HorizontalAlignment="Center" TextWrapping="Wrap"/>
                        <TextBlock Text="Select an image to start editing" 
                                 Foreground="{StaticResource SecondaryTextBrush}"
                                 HorizontalAlignment="Center" TextWrapping="Wrap" Margin="0,4,0,0"/>
                    </StackPanel>
                </ContentControl>
            </Border>
        </Grid>

        <!-- Status Bar -->
        <Border Grid.Row="2" Background="{StaticResource TertiaryBackgroundBrush}"
                BorderBrush="{StaticResource BorderBrush}" BorderThickness="0,1,0,0" Height="28">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" x:Name="StatusText" Text="جاهز / Ready"
                         VerticalAlignment="Center" Margin="12,0"/>

                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center" Margin="12,0">
                    <TextBlock x:Name="MemoryUsage" Text="Memory: 0 MB" Margin="0,0,16,0"/>
                    <TextBlock x:Name="CurrentTime" Margin="0,0,0,0"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
