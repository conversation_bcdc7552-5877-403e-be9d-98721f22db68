using Microsoft.Extensions.Logging;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.PixelFormats;
using System.Diagnostics;

namespace ELashraffyEditor.Core.Services;

/// <summary>
/// معالج ملفات RAW
/// RAW image processor
/// </summary>
public interface IRawImageProcessor
{
    /// <summary>
    /// التحقق من دعم ملف RAW
    /// Check RAW file support
    /// </summary>
    bool IsRawFormatSupported(string extension);

    /// <summary>
    /// تحميل ملف RAW
    /// Load RAW file
    /// </summary>
    Task<Image<Rgba32>?> LoadRawImageAsync(string filePath);

    /// <summary>
    /// الحصول على معلومات ملف RAW
    /// Get RAW file information
    /// </summary>
    Task<RawImageInfo?> GetRawImageInfoAsync(string filePath);

    /// <summary>
    /// تحويل RAW إلى تنسيق قياسي
    /// Convert RAW to standard format
    /// </summary>
    Task<bool> ConvertRawToStandardAsync(string rawPath, string outputPath, RawProcessingSettings settings);
}

public class RawImageProcessor : IRawImageProcessor
{
    private readonly ILogger<RawImageProcessor> _logger;
    
    // تنسيقات RAW المدعومة
    // Supported RAW formats
    private readonly Dictionary<string, string> _rawFormats = new(StringComparer.OrdinalIgnoreCase)
    {
        { ".cr2", "Canon RAW" },
        { ".cr3", "Canon RAW v3" },
        { ".nef", "Nikon RAW" },
        { ".arw", "Sony RAW" },
        { ".dng", "Adobe DNG" },
        { ".orf", "Olympus RAW" },
        { ".rw2", "Panasonic RAW" },
        { ".pef", "Pentax RAW" },
        { ".srw", "Samsung RAW" },
        { ".raf", "Fujifilm RAW" },
        { ".3fr", "Hasselblad RAW" },
        { ".fff", "Imacon RAW" },
        { ".iiq", "Phase One RAW" },
        { ".k25", "Kodak RAW" },
        { ".kdc", "Kodak RAW" },
        { ".mrw", "Minolta RAW" },
        { ".raw", "Generic RAW" }
    };

    public RawImageProcessor(ILogger<RawImageProcessor> logger)
    {
        _logger = logger;
        _logger.LogInformation("RawImageProcessor initialized with {Count} supported formats", _rawFormats.Count);
    }

    public bool IsRawFormatSupported(string extension)
    {
        if (string.IsNullOrEmpty(extension)) return false;
        
        extension = extension.ToLowerInvariant();
        if (!extension.StartsWith("."))
            extension = "." + extension;

        return _rawFormats.ContainsKey(extension);
    }

    public async Task<Image<Rgba32>?> LoadRawImageAsync(string filePath)
    {
        try
        {
            if (!File.Exists(filePath))
            {
                _logger.LogWarning("RAW file not found: {FilePath}", filePath);
                return null;
            }

            var extension = Path.GetExtension(filePath).ToLowerInvariant();
            if (!IsRawFormatSupported(extension))
            {
                _logger.LogWarning("Unsupported RAW format: {Extension}", extension);
                return null;
            }

            // محاولة استخدام dcraw إذا كان متوفراً
            // Try using dcraw if available
            var dcrawImage = await TryLoadWithDcrawAsync(filePath);
            if (dcrawImage != null)
            {
                return dcrawImage;
            }

            // محاولة تحميل مباشر (قد يعمل مع DNG)
            // Try direct loading (might work with DNG)
            var directImage = await TryDirectLoadAsync(filePath);
            if (directImage != null)
            {
                return directImage;
            }

            // استخراج الصورة المصغرة المدمجة
            // Extract embedded thumbnail
            var thumbnailImage = await ExtractEmbeddedThumbnailAsync(filePath);
            if (thumbnailImage != null)
            {
                _logger.LogWarning("Loaded embedded thumbnail for RAW file: {FilePath}", filePath);
                return thumbnailImage;
            }

            _logger.LogError("Failed to load RAW image: {FilePath}", filePath);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading RAW image: {FilePath}", filePath);
            return null;
        }
    }

    public async Task<RawImageInfo?> GetRawImageInfoAsync(string filePath)
    {
        try
        {
            if (!File.Exists(filePath))
                return null;

            var fileInfo = new FileInfo(filePath);
            var extension = fileInfo.Extension.ToLowerInvariant();

            if (!_rawFormats.TryGetValue(extension, out var formatName))
                return null;

            var rawInfo = new RawImageInfo
            {
                FilePath = filePath,
                FileName = fileInfo.Name,
                Extension = extension,
                FormatName = formatName,
                FileSize = fileInfo.Length,
                DateCreated = fileInfo.CreationTime,
                DateModified = fileInfo.LastWriteTime
            };

            // محاولة قراءة معلومات إضافية
            // Try to read additional information
            await TryReadRawMetadataAsync(rawInfo);

            return rawInfo;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get RAW image info: {FilePath}", filePath);
            return null;
        }
    }

    public async Task<bool> ConvertRawToStandardAsync(string rawPath, string outputPath, RawProcessingSettings settings)
    {
        try
        {
            using var rawImage = await LoadRawImageAsync(rawPath);
            if (rawImage == null)
            {
                _logger.LogError("Failed to load RAW image for conversion: {RawPath}", rawPath);
                return false;
            }

            // تطبيق إعدادات المعالجة
            // Apply processing settings
            var processedImage = await ApplyRawProcessingAsync(rawImage, settings);

            // حفظ الصورة المعالجة
            // Save processed image
            var directory = Path.GetDirectoryName(outputPath);
            if (!string.IsNullOrEmpty(directory))
            {
                Directory.CreateDirectory(directory);
            }

            using var outputStream = File.Create(outputPath);
            await processedImage.SaveAsJpegAsync(outputStream);

            processedImage.Dispose();

            _logger.LogInformation("Successfully converted RAW to standard format: {RawPath} -> {OutputPath}", 
                rawPath, outputPath);
            
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to convert RAW to standard format: {RawPath}", rawPath);
            return false;
        }
    }

    private async Task<Image<Rgba32>?> TryLoadWithDcrawAsync(string filePath)
    {
        try
        {
            // التحقق من وجود dcraw
            // Check if dcraw is available
            var dcrawPath = FindDcrawExecutable();
            if (string.IsNullOrEmpty(dcrawPath))
            {
                _logger.LogDebug("dcraw not found, skipping dcraw loading method");
                return null;
            }

            // إنشاء ملف مؤقت للإخراج
            // Create temporary output file
            var tempOutputPath = Path.GetTempFileName() + ".ppm";

            try
            {
                // تشغيل dcraw
                // Run dcraw
                var processInfo = new ProcessStartInfo
                {
                    FileName = dcrawPath,
                    Arguments = $"-c -w \"{filePath}\"",
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    UseShellExecute = false,
                    CreateNoWindow = true
                };

                using var process = Process.Start(processInfo);
                if (process == null)
                {
                    _logger.LogWarning("Failed to start dcraw process");
                    return null;
                }

                // حفظ الإخراج إلى ملف مؤقت
                // Save output to temporary file
                using (var outputFile = File.Create(tempOutputPath))
                {
                    await process.StandardOutput.BaseStream.CopyToAsync(outputFile);
                }

                await process.WaitForExitAsync();

                if (process.ExitCode != 0)
                {
                    var error = await process.StandardError.ReadToEndAsync();
                    _logger.LogWarning("dcraw failed with exit code {ExitCode}: {Error}", process.ExitCode, error);
                    return null;
                }

                // تحميل الصورة من الملف المؤقت
                // Load image from temporary file
                using var tempStream = File.OpenRead(tempOutputPath);
                return await Image.LoadAsync<Rgba32>(tempStream);
            }
            finally
            {
                // تنظيف الملف المؤقت
                // Clean up temporary file
                if (File.Exists(tempOutputPath))
                {
                    File.Delete(tempOutputPath);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Failed to load RAW with dcraw: {FilePath}", filePath);
            return null;
        }
    }

    private async Task<Image<Rgba32>?> TryDirectLoadAsync(string filePath)
    {
        try
        {
            // محاولة تحميل مباشر (يعمل مع DNG وبعض التنسيقات الأخرى)
            // Try direct loading (works with DNG and some other formats)
            using var fileStream = File.OpenRead(filePath);
            return await Image.LoadAsync<Rgba32>(fileStream);
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Direct RAW loading failed: {FilePath}", filePath);
            return null;
        }
    }

    private async Task<Image<Rgba32>?> ExtractEmbeddedThumbnailAsync(string filePath)
    {
        try
        {
            // محاولة استخراج الصورة المصغرة المدمجة
            // Try to extract embedded thumbnail
            // هذا تنفيذ مبسط - في التطبيق الحقيقي ستحتاج إلى مكتبة متخصصة
            // This is a simplified implementation - in a real app you'd need a specialized library
            
            using var fileStream = File.OpenRead(filePath);
            var buffer = new byte[1024 * 1024]; // 1MB buffer
            var bytesRead = await fileStream.ReadAsync(buffer, 0, buffer.Length);

            // البحث عن بداية JPEG في البيانات
            // Look for JPEG start in the data
            for (int i = 0; i < bytesRead - 1; i++)
            {
                if (buffer[i] == 0xFF && buffer[i + 1] == 0xD8) // JPEG SOI marker
                {
                    // البحث عن نهاية JPEG
                    // Look for JPEG end
                    for (int j = i + 2; j < bytesRead - 1; j++)
                    {
                        if (buffer[j] == 0xFF && buffer[j + 1] == 0xD9) // JPEG EOI marker
                        {
                            var jpegData = new byte[j - i + 2];
                            Array.Copy(buffer, i, jpegData, 0, jpegData.Length);
                            
                            using var jpegStream = new MemoryStream(jpegData);
                            return await Image.LoadAsync<Rgba32>(jpegStream);
                        }
                    }
                }
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Failed to extract embedded thumbnail: {FilePath}", filePath);
            return null;
        }
    }

    private async Task TryReadRawMetadataAsync(RawImageInfo rawInfo)
    {
        try
        {
            // قراءة معلومات أساسية من ملف RAW
            // Read basic information from RAW file
            // هذا تنفيذ مبسط
            // This is a simplified implementation
            
            using var fileStream = File.OpenRead(rawInfo.FilePath);
            var buffer = new byte[1024];
            await fileStream.ReadAsync(buffer, 0, buffer.Length);

            // محاولة تحديد الكاميرا من البيانات
            // Try to determine camera from data
            rawInfo.CameraMake = DetectCameraMake(buffer);
            rawInfo.CameraModel = "Unknown";
            rawInfo.Width = 0;
            rawInfo.Height = 0;
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Failed to read RAW metadata: {FilePath}", rawInfo.FilePath);
        }
    }

    private string DetectCameraMake(byte[] buffer)
    {
        // كشف بسيط لصانع الكاميرا من البيانات
        // Simple camera make detection from data
        var dataString = System.Text.Encoding.ASCII.GetString(buffer).ToLowerInvariant();
        
        if (dataString.Contains("canon")) return "Canon";
        if (dataString.Contains("nikon")) return "Nikon";
        if (dataString.Contains("sony")) return "Sony";
        if (dataString.Contains("olympus")) return "Olympus";
        if (dataString.Contains("panasonic")) return "Panasonic";
        if (dataString.Contains("pentax")) return "Pentax";
        if (dataString.Contains("fujifilm")) return "Fujifilm";
        
        return "Unknown";
    }

    private async Task<Image<Rgba32>> ApplyRawProcessingAsync(Image<Rgba32> rawImage, RawProcessingSettings settings)
    {
        // تطبيق إعدادات معالجة RAW
        // Apply RAW processing settings
        var processedImage = rawImage.Clone();

        // هنا يمكن تطبيق تعديلات مثل:
        // Here you can apply adjustments like:
        // - White balance
        // - Exposure compensation
        // - Highlight/Shadow recovery
        // - Color grading
        
        return processedImage;
    }

    private string FindDcrawExecutable()
    {
        // البحث عن dcraw في المسارات الشائعة
        // Look for dcraw in common paths
        var possiblePaths = new[]
        {
            "dcraw.exe",
            @"C:\Program Files\dcraw\dcraw.exe",
            @"C:\Program Files (x86)\dcraw\dcraw.exe",
            Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "dcraw.exe"),
            Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Tools", "dcraw.exe")
        };

        foreach (var path in possiblePaths)
        {
            if (File.Exists(path))
            {
                return path;
            }
        }

        return string.Empty;
    }
}

/// <summary>
/// معلومات ملف RAW
/// RAW image information
/// </summary>
public class RawImageInfo
{
    public string FilePath { get; set; } = string.Empty;
    public string FileName { get; set; } = string.Empty;
    public string Extension { get; set; } = string.Empty;
    public string FormatName { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public DateTime DateCreated { get; set; }
    public DateTime DateModified { get; set; }
    public string CameraMake { get; set; } = string.Empty;
    public string CameraModel { get; set; } = string.Empty;
    public int Width { get; set; }
    public int Height { get; set; }
    public string ColorSpace { get; set; } = string.Empty;
    public int BitDepth { get; set; }
}

/// <summary>
/// إعدادات معالجة RAW
/// RAW processing settings
/// </summary>
public class RawProcessingSettings
{
    public float WhiteBalance { get; set; } = 1.0f;
    public float Exposure { get; set; } = 0.0f;
    public float Highlights { get; set; } = 0.0f;
    public float Shadows { get; set; } = 0.0f;
    public float Contrast { get; set; } = 0.0f;
    public float Saturation { get; set; } = 0.0f;
    public bool AutoWhiteBalance { get; set; } = true;
    public bool NoiseReduction { get; set; } = true;
    public float Sharpening { get; set; } = 0.5f;
}
