using Microsoft.Extensions.Logging;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Formats;
using SixLabors.ImageSharp.Formats.Jpeg;
using SixLabors.ImageSharp.Formats.Png;
using SixLabors.ImageSharp.Formats.Tiff;
using SixLabors.ImageSharp.Formats.Bmp;
using SixLabors.ImageSharp.Formats.Gif;
using SixLabors.ImageSharp.Formats.Webp;
using SixLabors.ImageSharp.PixelFormats;
using SixLabors.ImageSharp.Metadata.Profiles.Exif;

namespace ELashraffyEditor.Core.Services;

/// <summary>
/// خدمة دعم تنسيقات الملفات
/// File format support service
/// </summary>
public interface IFileFormatService
{
    /// <summary>
    /// التحقق من دعم التنسيق
    /// Check if format is supported
    /// </summary>
    bool IsFormatSupported(string extension);

    /// <summary>
    /// الحصول على التنسيقات المدعومة
    /// Get supported formats
    /// </summary>
    string[] GetSupportedFormats();

    /// <summary>
    /// الحصول على تنسيقات RAW المدعومة
    /// Get supported RAW formats
    /// </summary>
    string[] GetSupportedRawFormats();

    /// <summary>
    /// تحميل صورة مع دعم تنسيقات متعددة
    /// Load image with multi-format support
    /// </summary>
    Task<Image<Rgba32>?> LoadImageAsync(string filePath);

    /// <summary>
    /// حفظ صورة بتنسيق محدد
    /// Save image in specific format
    /// </summary>
    Task<bool> SaveImageAsync(Image<Rgba32> image, string filePath, string format, int quality = 95);

    /// <summary>
    /// الحصول على معلومات الملف
    /// Get file information
    /// </summary>
    Task<FileFormatInfo?> GetFileInfoAsync(string filePath);

    /// <summary>
    /// تحويل تنسيق الملف
    /// Convert file format
    /// </summary>
    Task<bool> ConvertFormatAsync(string inputPath, string outputPath, string targetFormat, int quality = 95);

    /// <summary>
    /// استخراج البيانات الوصفية
    /// Extract metadata
    /// </summary>
    Task<Dictionary<string, object>> ExtractMetadataAsync(string filePath);
}

public class FileFormatService : IFileFormatService
{
    private readonly ILogger<FileFormatService> _logger;
    
    // التنسيقات المدعومة
    // Supported formats
    private readonly Dictionary<string, IImageFormat> _supportedFormats;
    private readonly string[] _rawFormats = { ".cr2", ".nef", ".arw", ".dng", ".orf", ".rw2", ".pef", ".srw" };

    public FileFormatService(ILogger<FileFormatService> logger)
    {
        _logger = logger;
        
        _supportedFormats = new Dictionary<string, IImageFormat>(StringComparer.OrdinalIgnoreCase)
        {
            { ".jpg", JpegFormat.Instance },
            { ".jpeg", JpegFormat.Instance },
            { ".png", PngFormat.Instance },
            { ".tiff", TiffFormat.Instance },
            { ".tif", TiffFormat.Instance },
            { ".bmp", BmpFormat.Instance },
            { ".gif", GifFormat.Instance },
            { ".webp", WebpFormat.Instance }
        };

        _logger.LogInformation("FileFormatService initialized with {Count} supported formats", _supportedFormats.Count);
    }

    public bool IsFormatSupported(string extension)
    {
        if (string.IsNullOrEmpty(extension)) return false;
        
        extension = extension.ToLowerInvariant();
        if (!extension.StartsWith("."))
            extension = "." + extension;

        return _supportedFormats.ContainsKey(extension) || _rawFormats.Contains(extension);
    }

    public string[] GetSupportedFormats()
    {
        return _supportedFormats.Keys.Concat(_rawFormats).ToArray();
    }

    public string[] GetSupportedRawFormats()
    {
        return _rawFormats.ToArray();
    }

    public async Task<Image<Rgba32>?> LoadImageAsync(string filePath)
    {
        try
        {
            if (!File.Exists(filePath))
            {
                _logger.LogWarning("File not found: {FilePath}", filePath);
                return null;
            }

            var extension = Path.GetExtension(filePath).ToLowerInvariant();
            
            // التحقق من دعم التنسيق
            // Check format support
            if (!IsFormatSupported(extension))
            {
                _logger.LogWarning("Unsupported format: {Extension}", extension);
                return null;
            }

            // معالجة ملفات RAW
            // Handle RAW files
            if (_rawFormats.Contains(extension))
            {
                return await LoadRawImageAsync(filePath);
            }

            // تحميل الصور العادية
            // Load regular images
            using var fileStream = File.OpenRead(filePath);
            var image = await Image.LoadAsync<Rgba32>(fileStream);
            
            _logger.LogDebug("Successfully loaded image: {FilePath} ({Width}x{Height})", 
                filePath, image.Width, image.Height);
            
            return image;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load image: {FilePath}", filePath);
            return null;
        }
    }

    public async Task<bool> SaveImageAsync(Image<Rgba32> image, string filePath, string format, int quality = 95)
    {
        try
        {
            var extension = "." + format.ToLowerInvariant();
            if (!_supportedFormats.TryGetValue(extension, out var imageFormat))
            {
                _logger.LogWarning("Unsupported save format: {Format}", format);
                return false;
            }

            // إنشاء المجلد إذا لم يكن موجوداً
            // Create directory if it doesn't exist
            var directory = Path.GetDirectoryName(filePath);
            if (!string.IsNullOrEmpty(directory))
            {
                Directory.CreateDirectory(directory);
            }

            using var fileStream = File.Create(filePath);
            
            // تطبيق إعدادات الجودة حسب التنسيق
            // Apply quality settings based on format
            switch (imageFormat)
            {
                case JpegFormat:
                    var jpegEncoder = new JpegEncoder { Quality = quality };
                    await image.SaveAsJpegAsync(fileStream, jpegEncoder);
                    break;
                    
                case PngFormat:
                    var pngEncoder = new PngEncoder { CompressionLevel = PngCompressionLevel.BestCompression };
                    await image.SaveAsPngAsync(fileStream, pngEncoder);
                    break;
                    
                case TiffFormat:
                    var tiffEncoder = new TiffEncoder();
                    await image.SaveAsTiffAsync(fileStream, tiffEncoder);
                    break;
                    
                case WebpFormat:
                    var webpEncoder = new WebpEncoder { Quality = quality };
                    await image.SaveAsWebpAsync(fileStream, webpEncoder);
                    break;
                    
                default:
                    await image.SaveAsync(fileStream, imageFormat);
                    break;
            }

            _logger.LogDebug("Successfully saved image: {FilePath} (Format: {Format}, Quality: {Quality})", 
                filePath, format, quality);
            
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save image: {FilePath}", filePath);
            return false;
        }
    }

    public async Task<FileFormatInfo?> GetFileInfoAsync(string filePath)
    {
        try
        {
            if (!File.Exists(filePath))
                return null;

            var fileInfo = new FileInfo(filePath);
            var extension = fileInfo.Extension.ToLowerInvariant();

            var formatInfo = new FileFormatInfo
            {
                FilePath = filePath,
                FileName = fileInfo.Name,
                Extension = extension,
                FileSize = fileInfo.Length,
                DateCreated = fileInfo.CreationTime,
                DateModified = fileInfo.LastWriteTime,
                IsSupported = IsFormatSupported(extension),
                IsRawFormat = _rawFormats.Contains(extension)
            };

            // الحصول على معلومات الصورة
            // Get image information
            if (formatInfo.IsSupported)
            {
                try
                {
                    using var image = await LoadImageAsync(filePath);
                    if (image != null)
                    {
                        formatInfo.Width = image.Width;
                        formatInfo.Height = image.Height;
                        formatInfo.PixelFormat = image.PixelType.ToString();
                        formatInfo.ColorSpace = GetColorSpace(image);
                        formatInfo.HasMetadata = image.Metadata != null;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to get image dimensions for: {FilePath}", filePath);
                }
            }

            return formatInfo;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get file info: {FilePath}", filePath);
            return null;
        }
    }

    public async Task<bool> ConvertFormatAsync(string inputPath, string outputPath, string targetFormat, int quality = 95)
    {
        try
        {
            using var image = await LoadImageAsync(inputPath);
            if (image == null)
            {
                _logger.LogWarning("Failed to load source image for conversion: {InputPath}", inputPath);
                return false;
            }

            var success = await SaveImageAsync(image, outputPath, targetFormat, quality);
            
            if (success)
            {
                _logger.LogInformation("Successfully converted {InputPath} to {OutputPath} (Format: {Format})", 
                    inputPath, outputPath, targetFormat);
            }
            
            return success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to convert format from {InputPath} to {OutputPath}", inputPath, outputPath);
            return false;
        }
    }

    public async Task<Dictionary<string, object>> ExtractMetadataAsync(string filePath)
    {
        var metadata = new Dictionary<string, object>();

        try
        {
            using var image = await LoadImageAsync(filePath);
            if (image?.Metadata == null)
                return metadata;

            // EXIF data
            if (image.Metadata.ExifProfile != null)
            {
                foreach (var value in image.Metadata.ExifProfile.Values)
                {
                    try
                    {
                        var key = value.Tag.ToString();
                        var val = value.GetValue();
                        if (val != null)
                        {
                            metadata[$"EXIF_{key}"] = val;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogDebug(ex, "Failed to read EXIF value: {Tag}", value.Tag);
                    }
                }
            }

            // Basic image properties
            metadata["Width"] = image.Width;
            metadata["Height"] = image.Height;
            metadata["PixelFormat"] = image.PixelType.ToString();
            metadata["ColorSpace"] = GetColorSpace(image);

            _logger.LogDebug("Extracted {Count} metadata entries from {FilePath}", metadata.Count, filePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to extract metadata from: {FilePath}", filePath);
        }

        return metadata;
    }

    private async Task<Image<Rgba32>?> LoadRawImageAsync(string filePath)
    {
        try
        {
            // ملاحظة: هذا تنفيذ مبسط لملفات RAW
            // Note: This is a simplified RAW file implementation
            // في التطبيق الحقيقي، ستحتاج إلى مكتبة متخصصة مثل LibRaw
            // In a real application, you would need a specialized library like LibRaw
            
            _logger.LogWarning("RAW format support is limited. File: {FilePath}", filePath);
            
            // محاولة تحميل كصورة عادية (قد تعمل مع بعض ملفات RAW)
            // Try loading as regular image (might work with some RAW files)
            using var fileStream = File.OpenRead(filePath);
            return await Image.LoadAsync<Rgba32>(fileStream);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load RAW image: {FilePath}", filePath);
            return null;
        }
    }

    private string GetColorSpace(Image<Rgba32> image)
    {
        // تحديد مساحة الألوان
        // Determine color space
        if (image.Metadata.IccProfile != null)
        {
            return "ICC Profile";
        }
        
        return "sRGB"; // افتراضي
    }
}

/// <summary>
/// معلومات تنسيق الملف
/// File format information
/// </summary>
public class FileFormatInfo
{
    public string FilePath { get; set; } = string.Empty;
    public string FileName { get; set; } = string.Empty;
    public string Extension { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public DateTime DateCreated { get; set; }
    public DateTime DateModified { get; set; }
    public bool IsSupported { get; set; }
    public bool IsRawFormat { get; set; }
    public int Width { get; set; }
    public int Height { get; set; }
    public string PixelFormat { get; set; } = string.Empty;
    public string ColorSpace { get; set; } = string.Empty;
    public bool HasMetadata { get; set; }
}
