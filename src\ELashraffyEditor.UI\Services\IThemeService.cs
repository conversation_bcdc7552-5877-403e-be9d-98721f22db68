namespace ELashraffyEditor.UI.Services;

/// <summary>
/// واجهة خدمة الثيمات
/// Theme service interface
/// </summary>
public interface IThemeService
{
    /// <summary>
    /// تعيين الثيم الحالي
    /// Set current theme
    /// </summary>
    void SetTheme(string themeName);

    /// <summary>
    /// الحصول على الثيم الحالي
    /// Get current theme
    /// </summary>
    string GetCurrentTheme();

    /// <summary>
    /// الحصول على الثيمات المتاحة
    /// Get available themes
    /// </summary>
    string[] GetAvailableThemes();

    /// <summary>
    /// التحقق من دعم الثيم
    /// Check if theme is supported
    /// </summary>
    bool IsThemeSupported(string themeName);

    /// <summary>
    /// حدث تغيير الثيم
    /// Theme changed event
    /// </summary>
    event EventHandler<string>? ThemeChanged;
}
