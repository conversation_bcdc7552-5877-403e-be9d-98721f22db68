<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات محرر الأشرافي - ELashrafy Editor Settings</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/settings.css">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
</head>
<body class="dark-theme">
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <span class="material-icons">photo_camera</span>
                    <h1>محرر الأشرافي</h1>
                    <span class="subtitle">ELashrafy Editor</span>
                </div>
                <div class="language-toggle">
                    <button id="languageBtn" class="btn-icon" title="تغيير اللغة / Change Language">
                        <span class="material-icons">language</span>
                    </button>
                    <button id="themeBtn" class="btn-icon" title="تغيير الثيم / Change Theme">
                        <span class="material-icons">dark_mode</span>
                    </button>
                </div>
            </div>
        </header>

        <!-- Navigation -->
        <nav class="sidebar">
            <ul class="nav-menu">
                <li class="nav-item active">
                    <a href="#general" class="nav-link" data-section="general">
                        <span class="material-icons">settings</span>
                        <span class="nav-text">عام / General</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#appearance" class="nav-link" data-section="appearance">
                        <span class="material-icons">palette</span>
                        <span class="nav-text">المظهر / Appearance</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#performance" class="nav-link" data-section="performance">
                        <span class="material-icons">speed</span>
                        <span class="nav-text">الأداء / Performance</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#export" class="nav-link" data-section="export">
                        <span class="material-icons">file_download</span>
                        <span class="nav-text">التصدير / Export</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#scripting" class="nav-link" data-section="scripting">
                        <span class="material-icons">code</span>
                        <span class="nav-text">البرمجة النصية / Scripting</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#about" class="nav-link" data-section="about">
                        <span class="material-icons">info</span>
                        <span class="nav-text">حول / About</span>
                    </a>
                </li>
            </ul>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- General Settings -->
            <section id="general" class="settings-section active">
                <h2>الإعدادات العامة / General Settings</h2>
                
                <div class="settings-group">
                    <h3>اللغة والمنطقة / Language & Region</h3>
                    <div class="setting-item">
                        <label for="language">اللغة / Language:</label>
                        <select id="language" class="form-control">
                            <option value="ar-SA">العربية (السعودية)</option>
                            <option value="en-US">English (United States)</option>
                        </select>
                    </div>
                </div>

                <div class="settings-group">
                    <h3>الحفظ التلقائي / Auto Save</h3>
                    <div class="setting-item">
                        <label class="checkbox-label">
                            <input type="checkbox" id="autoSave" checked>
                            <span class="checkmark"></span>
                            تفعيل الحفظ التلقائي / Enable Auto Save
                        </label>
                    </div>
                    <div class="setting-item">
                        <label for="autoSaveInterval">فترة الحفظ التلقائي (دقائق) / Auto Save Interval (minutes):</label>
                        <input type="number" id="autoSaveInterval" class="form-control" value="5" min="1" max="60">
                    </div>
                </div>

                <div class="settings-group">
                    <h3>الملفات الحديثة / Recent Files</h3>
                    <div class="setting-item">
                        <label for="maxRecentFiles">عدد الملفات الحديثة / Max Recent Files:</label>
                        <input type="number" id="maxRecentFiles" class="form-control" value="20" min="5" max="50">
                    </div>
                </div>
            </section>

            <!-- Appearance Settings -->
            <section id="appearance" class="settings-section">
                <h2>إعدادات المظهر / Appearance Settings</h2>
                
                <div class="settings-group">
                    <h3>الثيم / Theme</h3>
                    <div class="setting-item">
                        <label for="theme">الثيم / Theme:</label>
                        <select id="theme" class="form-control">
                            <option value="dark">مظلم / Dark</option>
                            <option value="light">فاتح / Light</option>
                        </select>
                    </div>
                </div>

                <div class="settings-group">
                    <h3>الصور المصغرة / Thumbnails</h3>
                    <div class="setting-item">
                        <label for="thumbnailSize">حجم الصور المصغرة / Thumbnail Size:</label>
                        <input type="range" id="thumbnailSize" class="slider" min="100" max="300" value="200">
                        <span class="slider-value">200px</span>
                    </div>
                </div>

                <div class="settings-group">
                    <h3>العرض / Display</h3>
                    <div class="setting-item">
                        <label class="checkbox-label">
                            <input type="checkbox" id="showHistogram" checked>
                            <span class="checkmark"></span>
                            عرض الهيستوجرام / Show Histogram
                        </label>
                    </div>
                    <div class="setting-item">
                        <label class="checkbox-label">
                            <input type="checkbox" id="showMetadata" checked>
                            <span class="checkmark"></span>
                            عرض البيانات الوصفية / Show Metadata
                        </label>
                    </div>
                </div>
            </section>

            <!-- Performance Settings -->
            <section id="performance" class="settings-section">
                <h2>إعدادات الأداء / Performance Settings</h2>
                
                <div class="settings-group">
                    <h3>الذاكرة / Memory</h3>
                    <div class="setting-item">
                        <label for="maxMemory">الحد الأقصى لاستخدام الذاكرة (MB) / Max Memory Usage (MB):</label>
                        <input type="number" id="maxMemory" class="form-control" value="4096" min="1024" max="16384" step="512">
                    </div>
                </div>

                <div class="settings-group">
                    <h3>المعالجة / Processing</h3>
                    <div class="setting-item">
                        <label class="checkbox-label">
                            <input type="checkbox" id="enableGPU" checked>
                            <span class="checkmark"></span>
                            تفعيل تسريع GPU / Enable GPU Acceleration
                        </label>
                    </div>
                    <div class="setting-item">
                        <label class="checkbox-label">
                            <input type="checkbox" id="enableParallel" checked>
                            <span class="checkmark"></span>
                            تفعيل المعالجة المتوازية / Enable Parallel Processing
                        </label>
                    </div>
                </div>
            </section>

            <!-- Export Settings -->
            <section id="export" class="settings-section">
                <h2>إعدادات التصدير / Export Settings</h2>
                
                <div class="settings-group">
                    <h3>الافتراضي / Default</h3>
                    <div class="setting-item">
                        <label for="defaultFormat">التنسيق الافتراضي / Default Format:</label>
                        <select id="defaultFormat" class="form-control">
                            <option value="JPEG">JPEG</option>
                            <option value="PNG">PNG</option>
                            <option value="TIFF">TIFF</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <label for="defaultQuality">الجودة الافتراضية / Default Quality:</label>
                        <input type="range" id="defaultQuality" class="slider" min="50" max="100" value="95">
                        <span class="slider-value">95%</span>
                    </div>
                </div>

                <div class="settings-group">
                    <h3>البيانات الوصفية / Metadata</h3>
                    <div class="setting-item">
                        <label class="checkbox-label">
                            <input type="checkbox" id="preserveMetadata" checked>
                            <span class="checkmark"></span>
                            الحفاظ على البيانات الوصفية / Preserve Metadata
                        </label>
                    </div>
                </div>
            </section>

            <!-- Scripting Settings -->
            <section id="scripting" class="settings-section">
                <h2>إعدادات البرمجة النصية / Scripting Settings</h2>
                
                <div class="settings-group">
                    <h3>Lua</h3>
                    <div class="setting-item">
                        <label class="checkbox-label">
                            <input type="checkbox" id="enableLua" checked>
                            <span class="checkmark"></span>
                            تفعيل Lua / Enable Lua
                        </label>
                    </div>
                </div>

                <div class="settings-group">
                    <h3>Python</h3>
                    <div class="setting-item">
                        <label class="checkbox-label">
                            <input type="checkbox" id="enablePython" checked>
                            <span class="checkmark"></span>
                            تفعيل Python / Enable Python
                        </label>
                    </div>
                </div>
            </section>

            <!-- About Section -->
            <section id="about" class="settings-section">
                <h2>حول البرنامج / About</h2>
                
                <div class="about-content">
                    <div class="app-info">
                        <h3>محرر الأشرافي / ELashrafy Editor</h3>
                        <p>الإصدار / Version: 1.0.0</p>
                        <p>حقوق الطبع والنشر © 2024 الأشرافي</p>
                        <p>Copyright © 2024 ELashrafy</p>
                    </div>
                    
                    <div class="tech-stack">
                        <h4>التقنيات المستخدمة / Technologies Used:</h4>
                        <ul>
                            <li>C# .NET 6 WPF</li>
                            <li>Material Design</li>
                            <li>WebView2</li>
                            <li>ImageSharp</li>
                            <li>Lua Scripting</li>
                            <li>Python Integration</li>
                            <li>Rust Performance Modules</li>
                        </ul>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-content">
            <button id="saveBtn" class="btn btn-primary">
                <span class="material-icons">save</span>
                حفظ / Save
            </button>
            <button id="resetBtn" class="btn btn-secondary">
                <span class="material-icons">refresh</span>
                إعادة تعيين / Reset
            </button>
        </div>
    </footer>

    <script src="scripts/main.js"></script>
    <script src="scripts/settings.js"></script>
</body>
</html>
