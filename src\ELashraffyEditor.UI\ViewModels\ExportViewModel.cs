using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;
using ELashraffyEditor.Core.Models;
using ELashraffyEditor.Core.Services;
using ELashraffyEditor.UI.Services;
using Microsoft.Extensions.Logging;
using System.IO;

namespace ELashraffyEditor.UI.ViewModels;

/// <summary>
/// ViewModel لوضع التصدير
/// Export mode view model
/// </summary>
public partial class ExportViewModel : ObservableObject
{
    private readonly ILogger<ExportViewModel> _logger;
    private readonly IImageProcessingService _imageProcessingService;
    private readonly IDialogService _dialogService;
    private readonly ISettingsService _settingsService;

    [ObservableProperty]
    private ObservableCollection<PhotoItem> _photosToExport = new();

    [ObservableProperty]
    private string _outputFolder = string.Empty;

    [ObservableProperty]
    private string _selectedFormat = "JPEG";

    [ObservableProperty]
    private int _quality = 95;

    [ObservableProperty]
    private bool _preserveMetadata = true;

    [ObservableProperty]
    private bool _resizeImages;

    [ObservableProperty]
    private int _maxWidth = 1920;

    [ObservableProperty]
    private int _maxHeight = 1080;

    [ObservableProperty]
    private bool _maintainAspectRatio = true;

    [ObservableProperty]
    private string _fileNamePattern = "{FileName}_exported";

    [ObservableProperty]
    private bool _addWatermark;

    [ObservableProperty]
    private string _watermarkText = "ELashrafy Editor";

    [ObservableProperty]
    private bool _isExporting;

    [ObservableProperty]
    private int _exportProgress;

    [ObservableProperty]
    private string _exportStatus = string.Empty;

    [ObservableProperty]
    private int _totalFiles;

    [ObservableProperty]
    private int _processedFiles;

    [ObservableProperty]
    private int _failedFiles;

    public ObservableCollection<string> AvailableFormats { get; } = new()
    {
        "JPEG", "PNG", "TIFF", "BMP", "WebP"
    };

    public ObservableCollection<string> FileNamePatterns { get; } = new()
    {
        "{FileName}_exported",
        "{FileName}_{Date}",
        "{FileName}_{DateTime}",
        "Export_{Index}_{FileName}",
        "{FileName}_ELashrafy"
    };

    public ExportViewModel(
        ILogger<ExportViewModel> logger,
        IImageProcessingService imageProcessingService,
        IDialogService dialogService,
        ISettingsService settingsService)
    {
        _logger = logger;
        _imageProcessingService = imageProcessingService;
        _dialogService = dialogService;
        _settingsService = settingsService;

        LoadSettings();
        _logger.LogInformation("ExportViewModel initialized");
    }

    private void LoadSettings()
    {
        SelectedFormat = _settingsService.GetSetting("DefaultExportFormat", "JPEG");
        Quality = _settingsService.GetSetting("DefaultExportQuality", 95);
        PreserveMetadata = _settingsService.GetSetting("PreserveMetadata", true);
        OutputFolder = _settingsService.GetSetting("LastExportFolder", 
            Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyPictures), "ELashrafy Exports"));
    }

    [RelayCommand]
    private void AddPhotos(IEnumerable<PhotoItem> photos)
    {
        foreach (var photo in photos)
        {
            if (!PhotosToExport.Contains(photo))
            {
                PhotosToExport.Add(photo);
            }
        }
        
        TotalFiles = PhotosToExport.Count;
        _logger.LogInformation("Added {Count} photos to export queue", photos.Count());
    }

    [RelayCommand]
    private void RemovePhoto(PhotoItem photo)
    {
        PhotosToExport.Remove(photo);
        TotalFiles = PhotosToExport.Count;
    }

    [RelayCommand]
    private void ClearPhotos()
    {
        PhotosToExport.Clear();
        TotalFiles = 0;
        ProcessedFiles = 0;
        FailedFiles = 0;
        ExportProgress = 0;
    }

    [RelayCommand]
    private void SelectOutputFolder()
    {
        var folder = _dialogService.ShowFolderDialog("اختر مجلد التصدير / Select Export Folder");
        if (!string.IsNullOrEmpty(folder))
        {
            OutputFolder = folder;
            _settingsService.SetSetting("LastExportFolder", folder);
        }
    }

    [RelayCommand]
    private async Task StartExportAsync()
    {
        if (PhotosToExport.Count == 0)
        {
            _dialogService.ShowWarning("لا توجد صور للتصدير\nNo photos to export");
            return;
        }

        if (string.IsNullOrEmpty(OutputFolder))
        {
            _dialogService.ShowWarning("يرجى اختيار مجلد التصدير\nPlease select export folder");
            return;
        }

        try
        {
            IsExporting = true;
            ProcessedFiles = 0;
            FailedFiles = 0;
            ExportProgress = 0;

            // إنشاء مجلد التصدير إذا لم يكن موجوداً
            // Create export folder if it doesn't exist
            System.IO.Directory.CreateDirectory(OutputFolder);

            var progressDialog = _dialogService.ShowProgressDialog(
                "تصدير الصور / Exporting Photos",
                "جاري تصدير الصور... / Exporting photos...",
                true);

            try
            {
                for (int i = 0; i < PhotosToExport.Count; i++)
                {
                    if (progressDialog.IsCancelled) break;

                    var photo = PhotosToExport[i];
                    var progress = (int)((double)(i + 1) / PhotosToExport.Count * 100);
                    
                    ExportStatus = $"تصدير: {photo.FileName} / Exporting: {photo.FileName}";
                    progressDialog.UpdateProgress(progress, ExportStatus);

                    var success = await ExportSinglePhotoAsync(photo, i + 1);
                    if (success)
                    {
                        ProcessedFiles++;
                    }
                    else
                    {
                        FailedFiles++;
                    }

                    ExportProgress = progress;
                    await Task.Delay(10); // Small delay for UI updates
                }

                var message = $"تم تصدير {ProcessedFiles} صورة بنجاح";
                if (FailedFiles > 0)
                {
                    message += $"، فشل في تصدير {FailedFiles} صورة";
                }
                message += $"\nExported {ProcessedFiles} photos successfully";
                if (FailedFiles > 0)
                {
                    message += $", failed to export {FailedFiles} photos";
                }

                _dialogService.ShowInformation(message);
                _logger.LogInformation("Export completed: {Processed} successful, {Failed} failed", 
                    ProcessedFiles, FailedFiles);
            }
            finally
            {
                progressDialog.Close();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Export operation failed");
            _dialogService.ShowError($"فشل في عملية التصدير: {ex.Message}\nExport operation failed: {ex.Message}");
        }
        finally
        {
            IsExporting = false;
            ExportStatus = "مكتمل / Completed";
        }
    }

    [RelayCommand]
    private void SaveExportPreset()
    {
        var presetName = _dialogService.ShowTextInputDialog(
            "أدخل اسم الإعداد المسبق\nEnter preset name",
            "حفظ إعداد مسبق / Save Preset");

        if (!string.IsNullOrEmpty(presetName))
        {
            var preset = new ExportPreset
            {
                Name = presetName,
                Format = SelectedFormat,
                Quality = Quality,
                PreserveMetadata = PreserveMetadata,
                ResizeImages = ResizeImages,
                MaxWidth = MaxWidth,
                MaxHeight = MaxHeight,
                MaintainAspectRatio = MaintainAspectRatio,
                FileNamePattern = FileNamePattern,
                AddWatermark = AddWatermark,
                WatermarkText = WatermarkText
            };

            // حفظ الإعداد المسبق
            // Save preset
            SavePreset(preset);
            
            _dialogService.ShowInformation("تم حفظ الإعداد المسبق بنجاح\nPreset saved successfully");
        }
    }

    [RelayCommand]
    private void LoadExportPreset(ExportPreset preset)
    {
        SelectedFormat = preset.Format;
        Quality = preset.Quality;
        PreserveMetadata = preset.PreserveMetadata;
        ResizeImages = preset.ResizeImages;
        MaxWidth = preset.MaxWidth;
        MaxHeight = preset.MaxHeight;
        MaintainAspectRatio = preset.MaintainAspectRatio;
        FileNamePattern = preset.FileNamePattern;
        AddWatermark = preset.AddWatermark;
        WatermarkText = preset.WatermarkText;

        _logger.LogInformation("Export preset loaded: {PresetName}", preset.Name);
    }

    private async Task<bool> ExportSinglePhotoAsync(PhotoItem photo, int index)
    {
        try
        {
            // تحميل الصورة الأصلية
            // Load original image
            using var originalImage = await _imageProcessingService.LoadImageAsync(photo.FilePath);
            if (originalImage == null) return false;

            // تطبيق التعديلات
            // Apply adjustments
            using var adjustedImage = await _imageProcessingService.ApplyAdjustmentsAsync(originalImage, photo.Adjustments);
            if (adjustedImage == null) return false;

            var imageToExport = adjustedImage;

            // تغيير الحجم إذا لزم الأمر
            // Resize if needed
            if (ResizeImages)
            {
                imageToExport = await _imageProcessingService.ResizeImageAsync(
                    adjustedImage, MaxWidth, MaxHeight, MaintainAspectRatio);
                if (imageToExport == null) return false;
            }

            // إضافة العلامة المائية إذا لزم الأمر
            // Add watermark if needed
            if (AddWatermark && !string.IsNullOrEmpty(WatermarkText))
            {
                // تنفيذ إضافة العلامة المائية
                // Implement watermark addition
                // هذا يتطلب تنفيذ إضافي في ImageProcessingService
                // This requires additional implementation in ImageProcessingService
            }

            // إنشاء اسم الملف
            // Generate file name
            var fileName = GenerateFileName(photo, index);
            var outputPath = Path.Combine(OutputFolder, fileName);

            // حفظ الصورة
            // Save image
            var success = await _imageProcessingService.SaveImageAsync(
                imageToExport, outputPath, SelectedFormat.ToLowerInvariant(), Quality);

            // تنظيف الذاكرة
            // Clean up memory
            if (imageToExport != adjustedImage)
            {
                imageToExport.Dispose();
            }

            return success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to export photo: {FileName}", photo.FileName);
            return false;
        }
    }

    private string GenerateFileName(PhotoItem photo, int index)
    {
        var pattern = FileNamePattern;
        var fileName = Path.GetFileNameWithoutExtension(photo.FileName);
        var extension = SelectedFormat.ToLowerInvariant() switch
        {
            "jpeg" => ".jpg",
            _ => $".{SelectedFormat.ToLowerInvariant()}"
        };

        pattern = pattern.Replace("{FileName}", fileName);
        pattern = pattern.Replace("{Index}", index.ToString("D3"));
        pattern = pattern.Replace("{Date}", DateTime.Now.ToString("yyyy-MM-dd"));
        pattern = pattern.Replace("{DateTime}", DateTime.Now.ToString("yyyy-MM-dd_HH-mm-ss"));

        return pattern + extension;
    }

    private void SavePreset(ExportPreset preset)
    {
        // تنفيذ حفظ الإعداد المسبق
        // Implement preset saving
        // يمكن حفظه في ملف JSON أو قاعدة بيانات
        // Can be saved to JSON file or database
    }
}

/// <summary>
/// نموذج الإعداد المسبق للتصدير
/// Export preset model
/// </summary>
public class ExportPreset
{
    public string Name { get; set; } = string.Empty;
    public string Format { get; set; } = "JPEG";
    public int Quality { get; set; } = 95;
    public bool PreserveMetadata { get; set; } = true;
    public bool ResizeImages { get; set; }
    public int MaxWidth { get; set; } = 1920;
    public int MaxHeight { get; set; } = 1080;
    public bool MaintainAspectRatio { get; set; } = true;
    public string FileNamePattern { get; set; } = "{FileName}_exported";
    public bool AddWatermark { get; set; }
    public string WatermarkText { get; set; } = "ELashrafy Editor";
}
