<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">

    <!-- Custom Button Styles -->
    
    <!-- Primary Button Style -->
    <Style x:Key="PrimaryButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
        <Setter Property="Background" Value="{StaticResource AccentBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="{StaticResource MediumPadding}"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Effect" Value="{StaticResource DefaultShadow}"/>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{StaticResource AccentHoverBrush}"/>
                <Setter Property="Effect" Value="{StaticResource ElevatedShadow}"/>
            </Trigger>
            <Trigger Property="IsPressed" Value="True">
                <Setter Property="Background" Value="{StaticResource AccentPressedBrush}"/>
            </Trigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Background" Value="{StaticResource AccentDisabledBrush}"/>
                <Setter Property="Foreground" Value="{StaticResource DisabledTextBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- Secondary Button Style -->
    <Style x:Key="SecondaryButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignOutlinedButton}">
        <Setter Property="BorderBrush" Value="{StaticResource AccentBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource AccentBrush}"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="{StaticResource ThinBorder}"/>
        <Setter Property="Padding" Value="{StaticResource MediumPadding}"/>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{StaticResource AccentBrush}"/>
                <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- Icon Button Style -->
    <Style x:Key="IconButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignFlatButton}">
        <Setter Property="Width" Value="40"/>
        <Setter Property="Height" Value="40"/>
        <Setter Property="Padding" Value="8"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Foreground" Value="{StaticResource SecondaryTextBrush}"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{StaticResource TertiaryBackgroundBrush}"/>
                <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- Custom TextBox Styles -->
    
    <!-- Modern TextBox Style -->
    <Style x:Key="ModernTextBoxStyle" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignOutlinedTextBox}">
        <Setter Property="Background" Value="{StaticResource SecondaryBackgroundBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="CaretBrush" Value="{StaticResource AccentBrush}"/>
        <Setter Property="SelectionBrush" Value="{StaticResource SelectionBrush}"/>
        <Setter Property="Padding" Value="{StaticResource MediumPadding}"/>
        <Setter Property="FontSize" Value="{StaticResource BodyFontSize}"/>
        <Style.Triggers>
            <Trigger Property="IsFocused" Value="True">
                <Setter Property="BorderBrush" Value="{StaticResource AccentBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- Custom Slider Styles -->
    
    <!-- Photo Adjustment Slider Style -->
    <Style x:Key="PhotoAdjustmentSliderStyle" TargetType="Slider" BasedOn="{StaticResource MaterialDesignSlider}">
        <Setter Property="Foreground" Value="{StaticResource AccentBrush}"/>
        <Setter Property="Background" Value="{StaticResource TertiaryBackgroundBrush}"/>
        <Setter Property="Height" Value="24"/>
        <Setter Property="Margin" Value="{StaticResource SmallMargin}"/>
        <Setter Property="TickPlacement" Value="None"/>
        <Setter Property="IsSnapToTickEnabled" Value="False"/>
        <Setter Property="IsMoveToPointEnabled" Value="True"/>
    </Style>

    <!-- Custom Panel Styles -->
    
    <!-- Card Panel Style -->
    <Style x:Key="CardPanelStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource SecondaryBackgroundBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="{StaticResource ThinBorder}"/>
        <Setter Property="CornerRadius" Value="{StaticResource MediumCornerRadius}"/>
        <Setter Property="Padding" Value="{StaticResource MediumPadding}"/>
        <Setter Property="Margin" Value="{StaticResource SmallMargin}"/>
        <Setter Property="Effect" Value="{StaticResource DefaultShadow}"/>
    </Style>

    <!-- Elevated Panel Style -->
    <Style x:Key="ElevatedPanelStyle" TargetType="Border" BasedOn="{StaticResource CardPanelStyle}">
        <Setter Property="Effect" Value="{StaticResource ElevatedShadow}"/>
        <Setter Property="Background" Value="{StaticResource TertiaryBackgroundBrush}"/>
    </Style>

    <!-- Custom ListBox Styles -->
    
    <!-- Photo Thumbnail ListBox Style -->
    <Style x:Key="PhotoThumbnailListBoxStyle" TargetType="ListBox">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="ScrollViewer.HorizontalScrollBarVisibility" Value="Disabled"/>
        <Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="Auto"/>
        <Setter Property="ItemsPanel">
            <Setter.Value>
                <ItemsPanelTemplate>
                    <WrapPanel Orientation="Horizontal"/>
                </ItemsPanelTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Photo Thumbnail Item Style -->
    <Style x:Key="PhotoThumbnailItemStyle" TargetType="ListBoxItem">
        <Setter Property="Margin" Value="{StaticResource SmallMargin}"/>
        <Setter Property="Padding" Value="0"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="2"/>
        <Setter Property="BorderBrush" Value="Transparent"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ListBoxItem">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{StaticResource SmallCornerRadius}">
                        <ContentPresenter Margin="{TemplateBinding Padding}"/>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="BorderBrush" Value="{StaticResource HighlightBrush}"/>
            </Trigger>
            <Trigger Property="IsSelected" Value="True">
                <Setter Property="BorderBrush" Value="{StaticResource AccentBrush}"/>
                <Setter Property="Background" Value="{StaticResource TertiaryBackgroundBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- Custom ScrollBar Styles -->
    
    <!-- Dark ScrollBar Style -->
    <Style x:Key="DarkScrollBarStyle" TargetType="ScrollBar">
        <Setter Property="Background" Value="{StaticResource SecondaryBackgroundBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource TertiaryBackgroundBrush}"/>
        <Setter Property="Width" Value="12"/>
        <Setter Property="MinWidth" Value="12"/>
    </Style>

    <!-- Custom Menu Styles -->
    
    <!-- Context Menu Style -->
    <Style x:Key="ContextMenuStyle" TargetType="ContextMenu">
        <Setter Property="Background" Value="{StaticResource SecondaryBackgroundBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="{StaticResource ThinBorder}"/>
        <Setter Property="Effect" Value="{StaticResource FloatingShadow}"/>
        <Setter Property="Padding" Value="{StaticResource SmallPadding}"/>
    </Style>

    <!-- Menu Item Style -->
    <Style x:Key="MenuItemStyle" TargetType="MenuItem">
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
        <Setter Property="Padding" Value="{StaticResource MediumPadding}"/>
        <Style.Triggers>
            <Trigger Property="IsHighlighted" Value="True">
                <Setter Property="Background" Value="{StaticResource AccentBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- Custom ProgressBar Style -->
    
    <!-- Modern ProgressBar Style -->
    <Style x:Key="ModernProgressBarStyle" TargetType="ProgressBar">
        <Setter Property="Background" Value="{StaticResource TertiaryBackgroundBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource AccentBrush}"/>
        <Setter Property="Height" Value="6"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ProgressBar">
                    <Border Background="{TemplateBinding Background}"
                            CornerRadius="{StaticResource SmallCornerRadius}">
                        <Rectangle Name="PART_Track" Fill="{TemplateBinding Foreground}"
                                 HorizontalAlignment="Left"
                                 RadiusX="3" RadiusY="3"/>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>
