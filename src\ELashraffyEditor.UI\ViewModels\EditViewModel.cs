using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using ELashraffyEditor.Core.Models;
using ELashraffyEditor.Core.Services;
using ELashraffyEditor.UI.Services;
using Microsoft.Extensions.Logging;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.PixelFormats;

namespace ELashraffyEditor.UI.ViewModels;

/// <summary>
/// ViewModel لوضع التحرير
/// Edit mode view model
/// </summary>
public partial class EditViewModel : ObservableObject
{
    private readonly ILogger<EditViewModel> _logger;
    private readonly IImageProcessingService _imageProcessingService;
    private readonly IDialogService _dialogService;
    private Image<Rgba32>? _originalImage;
    private Image<Rgba32>? _previewImage;

    [ObservableProperty]
    private PhotoItem? _currentPhoto;

    [ObservableProperty]
    private ImageAdjustment _adjustments = new();

    [ObservableProperty]
    private bool _isProcessing;

    [ObservableProperty]
    private bool _showBeforeAfter;

    [ObservableProperty]
    private bool _showHistogram = true;

    [ObservableProperty]
    private int[][] _histogram = new int[3][] { new int[256], new int[256], new int[256] };

    [ObservableProperty]
    private byte[]? _previewImageData;

    [ObservableProperty]
    private string _zoomLevel = "100%";

    [ObservableProperty]
    private bool _hasUnsavedChanges;

    public EditViewModel(
        ILogger<EditViewModel> logger,
        IImageProcessingService imageProcessingService,
        IDialogService dialogService)
    {
        _logger = logger;
        _imageProcessingService = imageProcessingService;
        _dialogService = dialogService;

        // مراقبة تغييرات التعديلات
        // Monitor adjustment changes
        Adjustments.PropertyChanged += OnAdjustmentsChanged;

        _logger.LogInformation("EditViewModel initialized");
    }

    private async void OnAdjustmentsChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
    {
        HasUnsavedChanges = true;
        await UpdatePreviewAsync();
    }

    [RelayCommand]
    private async Task LoadPhotoAsync(PhotoItem photo)
    {
        try
        {
            IsProcessing = true;
            CurrentPhoto = photo;

            _originalImage?.Dispose();
            _originalImage = await _imageProcessingService.LoadImageAsync(photo.FilePath);

            if (_originalImage != null)
            {
                // إعادة تعيين التعديلات
                // Reset adjustments
                Adjustments.PropertyChanged -= OnAdjustmentsChanged;
                Adjustments.CopyFrom(photo.Adjustments);
                Adjustments.PropertyChanged += OnAdjustmentsChanged;

                await UpdatePreviewAsync();
                await UpdateHistogramAsync();

                HasUnsavedChanges = false;
                _logger.LogInformation("Photo loaded for editing: {FileName}", photo.FileName);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load photo for editing");
            _dialogService.ShowError($"فشل في تحميل الصورة: {ex.Message}\nFailed to load photo: {ex.Message}");
        }
        finally
        {
            IsProcessing = false;
        }
    }

    [RelayCommand]
    private async Task ApplyAdjustmentsAsync()
    {
        if (_originalImage == null || CurrentPhoto == null) return;

        try
        {
            IsProcessing = true;

            var adjustedImage = await _imageProcessingService.ApplyAdjustmentsAsync(_originalImage, Adjustments);
            if (adjustedImage != null)
            {
                _previewImage?.Dispose();
                _previewImage = adjustedImage;

                await UpdatePreviewImageDataAsync();
                await UpdateHistogramAsync();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to apply adjustments");
            _dialogService.ShowError($"فشل في تطبيق التعديلات: {ex.Message}\nFailed to apply adjustments: {ex.Message}");
        }
        finally
        {
            IsProcessing = false;
        }
    }

    [RelayCommand]
    private void ResetAdjustments()
    {
        if (_dialogService.ShowConfirmation("هل تريد إعادة تعيين جميع التعديلات؟\nDo you want to reset all adjustments?"))
        {
            Adjustments.PropertyChanged -= OnAdjustmentsChanged;
            Adjustments.Reset();
            Adjustments.PropertyChanged += OnAdjustmentsChanged;

            _ = UpdatePreviewAsync();
            HasUnsavedChanges = true;
        }
    }

    [RelayCommand]
    private async Task AutoEnhanceAsync()
    {
        if (_originalImage == null) return;

        try
        {
            IsProcessing = true;

            var autoAdjustments = await _imageProcessingService.AutoEnhanceAsync(_originalImage);
            
            Adjustments.PropertyChanged -= OnAdjustmentsChanged;
            Adjustments.CopyFrom(autoAdjustments);
            Adjustments.PropertyChanged += OnAdjustmentsChanged;

            await UpdatePreviewAsync();
            HasUnsavedChanges = true;

            _logger.LogInformation("Auto enhancement applied");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to apply auto enhancement");
            _dialogService.ShowError($"فشل في التحسين التلقائي: {ex.Message}\nFailed to apply auto enhancement: {ex.Message}");
        }
        finally
        {
            IsProcessing = false;
        }
    }

    [RelayCommand]
    private async Task SaveChangesAsync()
    {
        if (CurrentPhoto == null || !HasUnsavedChanges) return;

        try
        {
            IsProcessing = true;

            // نسخ التعديلات إلى الصورة
            // Copy adjustments to photo
            CurrentPhoto.Adjustments.CopyFrom(Adjustments);

            HasUnsavedChanges = false;
            _logger.LogInformation("Changes saved for photo: {FileName}", CurrentPhoto.FileName);

            _dialogService.ShowInformation("تم حفظ التعديلات بنجاح\nChanges saved successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save changes");
            _dialogService.ShowError($"فشل في حفظ التعديلات: {ex.Message}\nFailed to save changes: {ex.Message}");
        }
        finally
        {
            IsProcessing = false;
        }
    }

    [RelayCommand]
    private async Task ExportPhotoAsync()
    {
        if (_originalImage == null || CurrentPhoto == null) return;

        try
        {
            var fileName = Path.GetFileNameWithoutExtension(CurrentPhoto.FileName) + "_edited.jpg";
            var filePath = _dialogService.ShowSaveFileDialog(
                "JPEG Files|*.jpg|PNG Files|*.png|TIFF Files|*.tiff",
                "تصدير الصورة / Export Photo",
                fileName);

            if (!string.IsNullOrEmpty(filePath))
            {
                IsProcessing = true;

                var adjustedImage = await _imageProcessingService.ApplyAdjustmentsAsync(_originalImage, Adjustments);
                if (adjustedImage != null)
                {
                    var format = Path.GetExtension(filePath).TrimStart('.').ToLowerInvariant();
                    var success = await _imageProcessingService.SaveImageAsync(adjustedImage, filePath, format);

                    if (success)
                    {
                        _dialogService.ShowInformation("تم تصدير الصورة بنجاح\nPhoto exported successfully");
                        _logger.LogInformation("Photo exported: {FilePath}", filePath);
                    }
                    else
                    {
                        _dialogService.ShowError("فشل في تصدير الصورة\nFailed to export photo");
                    }

                    adjustedImage.Dispose();
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to export photo");
            _dialogService.ShowError($"فشل في تصدير الصورة: {ex.Message}\nFailed to export photo: {ex.Message}");
        }
        finally
        {
            IsProcessing = false;
        }
    }

    [RelayCommand]
    private void ToggleBeforeAfter()
    {
        ShowBeforeAfter = !ShowBeforeAfter;
    }

    [RelayCommand]
    private void ToggleHistogram()
    {
        ShowHistogram = !ShowHistogram;
    }

    [RelayCommand]
    private void ZoomIn()
    {
        // تنفيذ التكبير
        // Implement zoom in
        UpdateZoomLevel(1.2f);
    }

    [RelayCommand]
    private void ZoomOut()
    {
        // تنفيذ التصغير
        // Implement zoom out
        UpdateZoomLevel(0.8f);
    }

    [RelayCommand]
    private void ZoomFit()
    {
        // ملائمة الصورة للشاشة
        // Fit image to screen
        ZoomLevel = "Fit";
    }

    [RelayCommand]
    private void ZoomActualSize()
    {
        // الحجم الفعلي
        // Actual size
        ZoomLevel = "100%";
    }

    private async Task UpdatePreviewAsync()
    {
        if (_originalImage == null) return;

        try
        {
            _previewImage?.Dispose();
            _previewImage = await _imageProcessingService.ApplyAdjustmentsAsync(_originalImage, Adjustments);
            
            await UpdatePreviewImageDataAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update preview");
        }
    }

    private async Task UpdatePreviewImageDataAsync()
    {
        if (_previewImage == null) return;

        try
        {
            using var memoryStream = new MemoryStream();
            await _previewImage.SaveAsJpegAsync(memoryStream);
            PreviewImageData = memoryStream.ToArray();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update preview image data");
        }
    }

    private async Task UpdateHistogramAsync()
    {
        if (_previewImage == null) return;

        try
        {
            Histogram = await _imageProcessingService.GenerateHistogramAsync(_previewImage);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update histogram");
        }
    }

    private void UpdateZoomLevel(float factor)
    {
        // تنفيذ تغيير مستوى التكبير
        // Implement zoom level change
        if (float.TryParse(ZoomLevel.TrimEnd('%'), out var currentZoom))
        {
            var newZoom = Math.Clamp(currentZoom * factor, 10, 500);
            ZoomLevel = $"{newZoom:F0}%";
        }
    }

    public void Dispose()
    {
        _originalImage?.Dispose();
        _previewImage?.Dispose();
    }
}
