<UserControl x:Class="ELashraffyEditor.UI.Controls.HistogramControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d" 
             d:DesignHeight="200" d:DesignWidth="300">
    
    <Border Background="{DynamicResource MaterialDesignDarkBackground}"
            BorderBrush="{DynamicResource MaterialDesignDivider}" 
            BorderThickness="1"
            CornerRadius="4">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- عنوان الهيستوجرام / Histogram Title -->
            <TextBlock Grid.Row="0" 
                       Text="الهيستوجرام / Histogram" 
                       Style="{StaticResource MaterialDesignCaptionTextBlock}"
                       HorizontalAlignment="Center"
                       Margin="5,5,5,0"/>

            <!-- منطقة رسم الهيستوجرام / Histogram Drawing Area -->
            <Canvas Grid.Row="1" 
                    Name="HistogramCanvas" 
                    Margin="10,5,10,5"
                    Background="Transparent"
                    SizeChanged="HistogramCanvas_SizeChanged"/>

            <!-- مفاتيح الألوان / Color Legend -->
            <StackPanel Grid.Row="2" 
                        Orientation="Horizontal" 
                        HorizontalAlignment="Center"
                        Margin="5">
                
                <!-- أحمر / Red -->
                <StackPanel Orientation="Horizontal" Margin="5,0">
                    <Rectangle Width="12" Height="12" Fill="Red" Opacity="0.7"/>
                    <TextBlock Text="R" Margin="3,0" FontSize="10" VerticalAlignment="Center"/>
                </StackPanel>
                
                <!-- أخضر / Green -->
                <StackPanel Orientation="Horizontal" Margin="5,0">
                    <Rectangle Width="12" Height="12" Fill="Green" Opacity="0.7"/>
                    <TextBlock Text="G" Margin="3,0" FontSize="10" VerticalAlignment="Center"/>
                </StackPanel>
                
                <!-- أزرق / Blue -->
                <StackPanel Orientation="Horizontal" Margin="5,0">
                    <Rectangle Width="12" Height="12" Fill="Blue" Opacity="0.7"/>
                    <TextBlock Text="B" Margin="3,0" FontSize="10" VerticalAlignment="Center"/>
                </StackPanel>
                
                <!-- مضيء / Luminance -->
                <StackPanel Orientation="Horizontal" Margin="5,0">
                    <Rectangle Width="12" Height="12" Fill="White" Opacity="0.7"/>
                    <TextBlock Text="L" Margin="3,0" FontSize="10" VerticalAlignment="Center"/>
                </StackPanel>
            </StackPanel>
        </Grid>
    </Border>
</UserControl>
