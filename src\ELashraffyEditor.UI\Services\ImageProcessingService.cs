using ELashraffyEditor.Core.Models;
using ELashraffyEditor.Core.Services;
using Microsoft.Extensions.Logging;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.PixelFormats;
using SixLabors.ImageSharp.Processing;
using SixLabors.ImageSharp.Formats;
using SixLabors.ImageSharp.Formats.Jpeg;
using SixLabors.ImageSharp.Formats.Png;
using SixLabors.ImageSharp.Formats.Tiff;
using System.IO;

namespace ELashraffyEditor.UI.Services;

/// <summary>
/// خدمة معالجة الصور
/// Image processing service
/// </summary>
public class ImageProcessingService : IImageProcessingService
{
    private readonly ILogger<ImageProcessingService> _logger;
    private readonly string[] _supportedFormats = { ".jpg", ".jpeg", ".png", ".tiff", ".tif", ".bmp", ".gif", ".webp" };

    public ImageProcessingService(ILogger<ImageProcessingService> logger)
    {
        _logger = logger;
    }

    public async Task<Image<Rgba32>?> LoadImageAsync(string filePath)
    {
        try
        {
            if (!File.Exists(filePath))
            {
                _logger.LogWarning("File not found: {FilePath}", filePath);
                return null;
            }

            using var fileStream = File.OpenRead(filePath);
            var image = await Image.LoadAsync<Rgba32>(fileStream);
            
            _logger.LogInformation("Image loaded successfully: {FilePath}, Size: {Width}x{Height}", 
                filePath, image.Width, image.Height);
            
            return image;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load image: {FilePath}", filePath);
            return null;
        }
    }

    public async Task<bool> SaveImageAsync(Image<Rgba32> image, string filePath, string format = "jpeg", int quality = 95)
    {
        try
        {
            var directory = Path.GetDirectoryName(filePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            IImageEncoder encoder = format.ToLowerInvariant() switch
            {
                "jpeg" or "jpg" => new JpegEncoder { Quality = quality },
                "png" => new PngEncoder(),
                "tiff" or "tif" => new TiffEncoder(),
                _ => new JpegEncoder { Quality = quality }
            };

            await image.SaveAsync(filePath, encoder);
            
            _logger.LogInformation("Image saved successfully: {FilePath}, Format: {Format}", filePath, format);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save image: {FilePath}", filePath);
            return false;
        }
    }

    public async Task<byte[]?> GenerateThumbnailAsync(string filePath, int maxWidth = 200, int maxHeight = 200)
    {
        try
        {
            using var image = await LoadImageAsync(filePath);
            if (image == null) return null;

            // حساب الأبعاد الجديدة مع الحفاظ على النسبة
            // Calculate new dimensions while maintaining aspect ratio
            var aspectRatio = (double)image.Width / image.Height;
            int newWidth, newHeight;

            if (aspectRatio > 1) // عرض أكبر من الارتفاع
            {
                newWidth = Math.Min(maxWidth, image.Width);
                newHeight = (int)(newWidth / aspectRatio);
            }
            else // ارتفاع أكبر من العرض أو مربع
            {
                newHeight = Math.Min(maxHeight, image.Height);
                newWidth = (int)(newHeight * aspectRatio);
            }

            image.Mutate(x => x.Resize(newWidth, newHeight));

            using var memoryStream = new MemoryStream();
            await image.SaveAsync(memoryStream, new JpegEncoder { Quality = 85 });
            
            _logger.LogDebug("Thumbnail generated: {FilePath}, Size: {Width}x{Height}", 
                filePath, newWidth, newHeight);
            
            return memoryStream.ToArray();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate thumbnail: {FilePath}", filePath);
            return null;
        }
    }

    public async Task<Image<Rgba32>?> ApplyAdjustmentsAsync(Image<Rgba32> originalImage, ImageAdjustment adjustments)
    {
        try
        {
            var image = originalImage.Clone();

            image.Mutate(x =>
            {
                // تطبيق التعديلات
                // Apply adjustments
                if (adjustments.Brightness != 0)
                    x.Brightness(1.0f + adjustments.Brightness / 100f);

                if (adjustments.Contrast != 0)
                    x.Contrast(1.0f + adjustments.Contrast / 100f);

                if (adjustments.Saturation != 0)
                    x.Saturate(1.0f + adjustments.Saturation / 100f);

                // تطبيق التعرض (محاكاة)
                // Apply exposure (simulation)
                if (adjustments.Exposure != 0)
                {
                    var exposureFactor = (float)Math.Pow(2, adjustments.Exposure);
                    x.Brightness(exposureFactor);
                }

                // تطبيق الحدة
                // Apply sharpness
                if (adjustments.Sharpness > 0)
                {
                    var sharpenAmount = adjustments.Sharpness / 100f;
                    x.GaussianSharpen(sharpenAmount);
                }
            });

            _logger.LogDebug("Adjustments applied successfully");
            return image;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to apply adjustments");
            return null;
        }
    }

    public async Task<PhotoItem?> GetImageInfoAsync(string filePath)
    {
        try
        {
            var fileInfo = new FileInfo(filePath);
            if (!fileInfo.Exists) return null;

            using var image = await LoadImageAsync(filePath);
            if (image == null) return null;

            var photoItem = new PhotoItem
            {
                FilePath = filePath,
                FileName = fileInfo.Name,
                DateCreated = fileInfo.CreationTime,
                DateModified = fileInfo.LastWriteTime,
                FileSize = fileInfo.Length,
                Width = image.Width,
                Height = image.Height,
                Format = Path.GetExtension(filePath).TrimStart('.').ToUpperInvariant(),
                IsRaw = IsRawFormat(filePath),
                ThumbnailData = await GenerateThumbnailAsync(filePath)
            };

            _logger.LogDebug("Image info extracted: {FileName}", photoItem.FileName);
            return photoItem;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get image info: {FilePath}", filePath);
            return null;
        }
    }

    public async Task<int[][]> GenerateHistogramAsync(Image<Rgba32> image)
    {
        try
        {
            var histogram = new int[3][]; // RGB
            for (int i = 0; i < 3; i++)
            {
                histogram[i] = new int[256];
            }

            await Task.Run(() =>
            {
                image.ProcessPixelRows(accessor =>
                {
                    for (int y = 0; y < accessor.Height; y++)
                    {
                        var row = accessor.GetRowSpan(y);
                        for (int x = 0; x < row.Length; x++)
                        {
                            var pixel = row[x];
                            histogram[0][pixel.R]++; // Red
                            histogram[1][pixel.G]++; // Green
                            histogram[2][pixel.B]++; // Blue
                        }
                    }
                });
            });

            _logger.LogDebug("Histogram generated successfully");
            return histogram;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate histogram");
            return new int[3][] { new int[256], new int[256], new int[256] };
        }
    }

    public async Task<Image<Rgba32>?> ApplyCustomFilterAsync(Image<Rgba32> image, string filterName, Dictionary<string, object> parameters)
    {
        try
        {
            var result = image.Clone();

            await Task.Run(() =>
            {
                result.Mutate(x =>
                {
                    switch (filterName.ToLowerInvariant())
                    {
                        case "blur":
                            var blurRadius = parameters.GetValueOrDefault("radius", 3.0f);
                            x.GaussianBlur((float)blurRadius);
                            break;

                        case "sepia":
                            x.Sepia();
                            break;

                        case "grayscale":
                            x.Grayscale();
                            break;

                        case "invert":
                            x.Invert();
                            break;

                        default:
                            _logger.LogWarning("Unknown filter: {FilterName}", filterName);
                            break;
                    }
                });
            });

            _logger.LogDebug("Custom filter applied: {FilterName}", filterName);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to apply custom filter: {FilterName}", filterName);
            return null;
        }
    }

    public async Task<ImageAdjustment> AutoEnhanceAsync(Image<Rgba32> image)
    {
        try
        {
            // تحسين تلقائي بسيط
            // Simple auto enhancement
            var adjustment = new ImageAdjustment();

            await Task.Run(() =>
            {
                // حساب الهيستوجرام لتحديد التحسينات المطلوبة
                // Calculate histogram to determine required enhancements
                var histogram = GenerateHistogramAsync(image).Result;
                
                // تحسين التباين بناءً على توزيع الهيستوجرام
                // Enhance contrast based on histogram distribution
                var totalPixels = image.Width * image.Height;
                var darkPixels = 0;
                var brightPixels = 0;

                for (int i = 0; i < 85; i++) // الثلث الأول
                    darkPixels += histogram[0][i] + histogram[1][i] + histogram[2][i];

                for (int i = 170; i < 256; i++) // الثلث الأخير
                    brightPixels += histogram[0][i] + histogram[1][i] + histogram[2][i];

                var darkRatio = (double)darkPixels / (totalPixels * 3);
                var brightRatio = (double)brightPixels / (totalPixels * 3);

                if (darkRatio > 0.3) // صورة مظلمة
                {
                    adjustment.Brightness = 15;
                    adjustment.Shadows = 20;
                }
                else if (brightRatio > 0.3) // صورة مضيئة
                {
                    adjustment.Brightness = -10;
                    adjustment.Highlights = -15;
                }

                // تحسين التباين
                adjustment.Contrast = 10;
                adjustment.Vibrance = 15;
            });

            _logger.LogDebug("Auto enhancement completed");
            return adjustment;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to auto enhance image");
            return new ImageAdjustment();
        }
    }

    public async Task<ImageAdjustment> AutoColorCorrectionAsync(Image<Rgba32> image)
    {
        // تصحيح الألوان التلقائي - تنفيذ مبسط
        // Auto color correction - simplified implementation
        return await AutoEnhanceAsync(image);
    }

    public async Task<Image<Rgba32>?> ReduceNoiseAsync(Image<Rgba32> image, float strength = 0.5f)
    {
        try
        {
            var result = image.Clone();
            await Task.Run(() =>
            {
                result.Mutate(x => x.GaussianBlur(strength));
            });

            _logger.LogDebug("Noise reduction applied with strength: {Strength}", strength);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to reduce noise");
            return null;
        }
    }

    public async Task<Image<Rgba32>?> SharpenImageAsync(Image<Rgba32> image, float amount = 1.0f)
    {
        try
        {
            var result = image.Clone();
            await Task.Run(() =>
            {
                result.Mutate(x => x.GaussianSharpen(amount));
            });

            _logger.LogDebug("Sharpening applied with amount: {Amount}", amount);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to sharpen image");
            return null;
        }
    }

    public async Task<Image<Rgba32>?> ResizeImageAsync(Image<Rgba32> image, int width, int height, bool maintainAspectRatio = true)
    {
        try
        {
            var result = image.Clone();
            await Task.Run(() =>
            {
                if (maintainAspectRatio)
                {
                    result.Mutate(x => x.Resize(width, height, KnownResamplers.Lanczos3));
                }
                else
                {
                    result.Mutate(x => x.Resize(new ResizeOptions
                    {
                        Size = new Size(width, height),
                        Mode = ResizeMode.Stretch
                    }));
                }
            });

            _logger.LogDebug("Image resized to: {Width}x{Height}", width, height);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to resize image");
            return null;
        }
    }

    public async Task<Image<Rgba32>?> CropImageAsync(Image<Rgba32> image, int x, int y, int width, int height)
    {
        try
        {
            var result = image.Clone();
            await Task.Run(() =>
            {
                result.Mutate(ctx => ctx.Crop(new Rectangle(x, y, width, height)));
            });

            _logger.LogDebug("Image cropped: {X},{Y} {Width}x{Height}", x, y, width, height);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to crop image");
            return null;
        }
    }

    public async Task<Image<Rgba32>?> RotateImageAsync(Image<Rgba32> image, float degrees)
    {
        try
        {
            var result = image.Clone();
            await Task.Run(() =>
            {
                result.Mutate(x => x.Rotate(degrees));
            });

            _logger.LogDebug("Image rotated by: {Degrees} degrees", degrees);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to rotate image");
            return null;
        }
    }

    public async Task<Image<Rgba32>?> FlipImageAsync(Image<Rgba32> image, bool horizontal = true)
    {
        try
        {
            var result = image.Clone();
            await Task.Run(() =>
            {
                if (horizontal)
                    result.Mutate(x => x.Flip(FlipMode.Horizontal));
                else
                    result.Mutate(x => x.Flip(FlipMode.Vertical));
            });

            _logger.LogDebug("Image flipped: {Direction}", horizontal ? "Horizontal" : "Vertical");
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to flip image");
            return null;
        }
    }

    public async Task<Image<Rgba32>?> ConvertToBlackAndWhiteAsync(Image<Rgba32> image)
    {
        try
        {
            var result = image.Clone();
            await Task.Run(() =>
            {
                result.Mutate(x => x.Grayscale());
            });

            _logger.LogDebug("Image converted to black and white");
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to convert to black and white");
            return null;
        }
    }

    public async Task<Image<Rgba32>?> ApplySepiaAsync(Image<Rgba32> image, float amount = 1.0f)
    {
        try
        {
            var result = image.Clone();
            await Task.Run(() =>
            {
                result.Mutate(x => x.Sepia(amount));
            });

            _logger.LogDebug("Sepia effect applied with amount: {Amount}", amount);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to apply sepia effect");
            return null;
        }
    }

    public async Task<Image<Rgba32>?> ApplyBlurAsync(Image<Rgba32> image, float radius = 3.0f)
    {
        try
        {
            var result = image.Clone();
            await Task.Run(() =>
            {
                result.Mutate(x => x.GaussianBlur(radius));
            });

            _logger.LogDebug("Blur effect applied with radius: {Radius}", radius);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to apply blur effect");
            return null;
        }
    }

    public async Task<Image<Rgba32>?> ApplyGlowAsync(Image<Rgba32> image, float radius = 10.0f, float strength = 0.5f)
    {
        // تأثير الوهج - تنفيذ مبسط
        // Glow effect - simplified implementation
        return await ApplyBlurAsync(image, radius);
    }

    public async Task<Image<Rgba32>?> ApplyVignetteAsync(Image<Rgba32> image, float strength = 0.5f)
    {
        try
        {
            var result = image.Clone();
            await Task.Run(() =>
            {
                result.Mutate(x => x.Vignette());
            });

            _logger.LogDebug("Vignette effect applied with strength: {Strength}", strength);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to apply vignette effect");
            return null;
        }
    }

    public string[] GetSupportedFormats()
    {
        return _supportedFormats;
    }

    public bool IsFormatSupported(string extension)
    {
        return _supportedFormats.Contains(extension.ToLowerInvariant());
    }

    private bool IsRawFormat(string filePath)
    {
        var extension = Path.GetExtension(filePath).ToLowerInvariant();
        var rawFormats = new[] { ".cr2", ".nef", ".arw", ".dng", ".raf", ".orf", ".rw2" };
        return rawFormats.Contains(extension);
    }
}
