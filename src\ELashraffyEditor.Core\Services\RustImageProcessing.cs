using System.Runtime.InteropServices;
using Microsoft.Extensions.Logging;

namespace ELashraffyEditor.Core.Services;

/// <summary>
/// خدمة معالجة الصور عالية الأداء باستخدام Rust
/// High-performance image processing service using Rust
/// </summary>
public class RustImageProcessing : IDisposable
{
    private readonly ILogger<RustImageProcessing> _logger;
    private bool _disposed = false;

    // هياكل البيانات المطابقة لـ Rust
    // Data structures matching Rust

    [StructLayout(LayoutKind.Sequential)]
    public struct ProcessingResult
    {
        public bool Success;
        public IntPtr ErrorMessage;
    }

    [StructLayout(LayoutKind.Sequential)]
    public struct NoiseReductionParams
    {
        public float Strength;
        [MarshalAs(UnmanagedType.I1)]
        public bool PreserveEdges;
        public int Iterations;
    }

    [StructLayout(LayoutKind.Sequential)]
    public struct SharpeningParams
    {
        public float Amount;
        public float Radius;
        public float Threshold;
    }

    // استيراد الدوال من مكتبة Rust
    // Import functions from Rust library

    [DllImport("elashrafy_image_processing.dll", CallingConvention = CallingConvention.Cdecl)]
    private static extern ProcessingResult reduce_noise_advanced(
        IntPtr imageData,
        int width,
        int height,
        ref NoiseReductionParams parameters,
        IntPtr outputData);

    [DllImport("elashrafy_image_processing.dll", CallingConvention = CallingConvention.Cdecl)]
    private static extern ProcessingResult sharpen_advanced(
        IntPtr imageData,
        int width,
        int height,
        ref SharpeningParams parameters,
        IntPtr outputData);

    [DllImport("elashrafy_image_processing.dll", CallingConvention = CallingConvention.Cdecl)]
    private static extern ProcessingResult auto_enhance_smart(
        IntPtr imageData,
        int width,
        int height,
        IntPtr outputData);

    [DllImport("elashrafy_image_processing.dll", CallingConvention = CallingConvention.Cdecl)]
    private static extern ProcessingResult process_batch_parallel(
        IntPtr[] images,
        int[] widths,
        int[] heights,
        int count,
        int operation,
        IntPtr[] outputs);

    [DllImport("elashrafy_image_processing.dll", CallingConvention = CallingConvention.Cdecl)]
    private static extern void free_error_string(IntPtr errorMessage);

    public RustImageProcessing(ILogger<RustImageProcessing> logger)
    {
        _logger = logger;
        _logger.LogInformation("RustImageProcessing initialized");
    }

    /// <summary>
    /// تقليل الضوضاء المتقدم
    /// Advanced noise reduction
    /// </summary>
    public async Task<byte[]?> ReduceNoiseAdvancedAsync(byte[] imageData, int width, int height, 
        float strength = 0.5f, bool preserveEdges = true, int iterations = 3)
    {
        return await Task.Run(() =>
        {
            try
            {
                var parameters = new NoiseReductionParams
                {
                    Strength = strength,
                    PreserveEdges = preserveEdges,
                    Iterations = iterations
                };

                var outputData = new byte[imageData.Length];
                
                var inputHandle = GCHandle.Alloc(imageData, GCHandleType.Pinned);
                var outputHandle = GCHandle.Alloc(outputData, GCHandleType.Pinned);

                ProcessingResult result = default;
                try
                {
                    result = reduce_noise_advanced(
                        inputHandle.AddrOfPinnedObject(),
                        width,
                        height,
                        ref parameters,
                        outputHandle.AddrOfPinnedObject());

                    if (!result.Success)
                    {
                        var errorMessage = GetErrorMessage(result.ErrorMessage);
                        _logger.LogError("Rust noise reduction failed: {Error}", errorMessage);
                        return null;
                    }

                    _logger.LogDebug("Noise reduction completed successfully");
                    return outputData;
                }
                finally
                {
                    inputHandle.Free();
                    outputHandle.Free();
                    if (result.ErrorMessage != IntPtr.Zero)
                    {
                        free_error_string(result.ErrorMessage);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to reduce noise");
                return null;
            }
        });
    }

    /// <summary>
    /// تحسين الحدة المتقدم
    /// Advanced sharpening
    /// </summary>
    public async Task<byte[]?> SharpenAdvancedAsync(byte[] imageData, int width, int height,
        float amount = 1.0f, float radius = 1.0f, float threshold = 0.1f)
    {
        return await Task.Run(() =>
        {
            try
            {
                var parameters = new SharpeningParams
                {
                    Amount = amount,
                    Radius = radius,
                    Threshold = threshold
                };

                var outputData = new byte[imageData.Length];
                
                var inputHandle = GCHandle.Alloc(imageData, GCHandleType.Pinned);
                var outputHandle = GCHandle.Alloc(outputData, GCHandleType.Pinned);

                ProcessingResult result = default;
                try
                {
                    result = sharpen_advanced(
                        inputHandle.AddrOfPinnedObject(),
                        width,
                        height,
                        ref parameters,
                        outputHandle.AddrOfPinnedObject());

                    if (!result.Success)
                    {
                        var errorMessage = GetErrorMessage(result.ErrorMessage);
                        _logger.LogError("Rust sharpening failed: {Error}", errorMessage);
                        return null;
                    }

                    _logger.LogDebug("Sharpening completed successfully");
                    return outputData;
                }
                finally
                {
                    inputHandle.Free();
                    outputHandle.Free();
                    if (result.ErrorMessage != IntPtr.Zero)
                    {
                        free_error_string(result.ErrorMessage);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to sharpen image");
                return null;
            }
        });
    }

    /// <summary>
    /// تحسين تلقائي ذكي
    /// Smart auto enhancement
    /// </summary>
    public async Task<byte[]?> AutoEnhanceSmartAsync(byte[] imageData, int width, int height)
    {
        return await Task.Run(() =>
        {
            try
            {
                var outputData = new byte[imageData.Length];
                
                var inputHandle = GCHandle.Alloc(imageData, GCHandleType.Pinned);
                var outputHandle = GCHandle.Alloc(outputData, GCHandleType.Pinned);

                ProcessingResult result = default;
                try
                {
                    result = auto_enhance_smart(
                        inputHandle.AddrOfPinnedObject(),
                        width,
                        height,
                        outputHandle.AddrOfPinnedObject());

                    if (!result.Success)
                    {
                        var errorMessage = GetErrorMessage(result.ErrorMessage);
                        _logger.LogError("Rust auto enhancement failed: {Error}", errorMessage);
                        return null;
                    }

                    _logger.LogDebug("Auto enhancement completed successfully");
                    return outputData;
                }
                finally
                {
                    inputHandle.Free();
                    outputHandle.Free();
                    if (result.ErrorMessage != IntPtr.Zero)
                    {
                        free_error_string(result.ErrorMessage);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to auto enhance image");
                return null;
            }
        });
    }

    /// <summary>
    /// معالجة متوازية للدفعات
    /// Parallel batch processing
    /// </summary>
    public async Task<List<byte[]?>> ProcessBatchParallelAsync(
        List<(byte[] data, int width, int height)> images,
        BatchOperation operation)
    {
        return await Task.Run(() =>
        {
            try
            {
                var count = images.Count;
                var inputHandles = new GCHandle[count];
                var outputHandles = new GCHandle[count];
                var inputPointers = new IntPtr[count];
                var outputPointers = new IntPtr[count];
                var widths = new int[count];
                var heights = new int[count];
                var outputs = new List<byte[]?>(count);

                ProcessingResult result = default;
                try
                {
                    // إعداد البيانات
                    // Setup data
                    for (int i = 0; i < count; i++)
                    {
                        var (data, width, height) = images[i];
                        var outputData = new byte[data.Length];

                        inputHandles[i] = GCHandle.Alloc(data, GCHandleType.Pinned);
                        outputHandles[i] = GCHandle.Alloc(outputData, GCHandleType.Pinned);

                        inputPointers[i] = inputHandles[i].AddrOfPinnedObject();
                        outputPointers[i] = outputHandles[i].AddrOfPinnedObject();

                        widths[i] = width;
                        heights[i] = height;
                        outputs.Add(outputData);
                    }

                    result = process_batch_parallel(
                        inputPointers,
                        widths,
                        heights,
                        count,
                        (int)operation,
                        outputPointers);

                    if (!result.Success)
                    {
                        var errorMessage = GetErrorMessage(result.ErrorMessage);
                        _logger.LogError("Rust batch processing failed: {Error}", errorMessage);
                        return outputs.Select(_ => (byte[]?)null).ToList();
                    }

                    _logger.LogInformation("Batch processing completed successfully for {Count} images", count);
                    return outputs;
                }
                finally
                {
                    // تنظيف الذاكرة
                    // Cleanup memory
                    for (int i = 0; i < count; i++)
                    {
                        if (inputHandles[i].IsAllocated)
                            inputHandles[i].Free();
                        if (outputHandles[i].IsAllocated)
                            outputHandles[i].Free();
                    }

                    if (result.ErrorMessage != IntPtr.Zero)
                    {
                        free_error_string(result.ErrorMessage);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process batch");
                return images.Select(_ => (byte[]?)null).ToList();
            }
        });
    }

    private string GetErrorMessage(IntPtr errorMessagePtr)
    {
        if (errorMessagePtr == IntPtr.Zero)
            return "Unknown error";

        try
        {
            return Marshal.PtrToStringAnsi(errorMessagePtr) ?? "Unknown error";
        }
        catch
        {
            return "Failed to read error message";
        }
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _disposed = true;
            _logger.LogInformation("RustImageProcessing disposed");
        }
    }
}

/// <summary>
/// عمليات المعالجة المتوازية
/// Batch processing operations
/// </summary>
public enum BatchOperation
{
    NoiseReduction = 0,
    Sharpening = 1,
    AutoEnhance = 2
}
