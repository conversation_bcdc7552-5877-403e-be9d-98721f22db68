using Microsoft.Extensions.Logging;
using System.Globalization;
using System.Resources;
using System.Threading;

namespace ELashraffyEditor.UI.Services;

/// <summary>
/// خدمة الترجمة والتوطين
/// Localization service
/// </summary>
public class LocalizationService : ILocalizationService
{
    private readonly ILogger<LocalizationService> _logger;
    private string _currentLanguage = "ar-SA";
    private readonly Dictionary<string, string> _supportedLanguages;
    private readonly Dictionary<string, Dictionary<string, string>> _localizedStrings;

    public event EventHandler<string>? LanguageChanged;

    public LocalizationService(ILogger<LocalizationService> logger)
    {
        _logger = logger;
        
        _supportedLanguages = new Dictionary<string, string>
        {
            { "ar-SA", "العربية" },
            { "en-US", "English" }
        };

        _localizedStrings = InitializeLocalizedStrings();
    }

    public void SetLanguage(string languageCode)
    {
        try
        {
            if (!IsLanguageSupported(languageCode))
            {
                _logger.LogWarning("Unsupported language: {LanguageCode}", languageCode);
                return;
            }

            _currentLanguage = languageCode;
            
            // تعيين ثقافة التطبيق
            // Set application culture
            var culture = new CultureInfo(languageCode);
            Thread.CurrentThread.CurrentCulture = culture;
            Thread.CurrentThread.CurrentUICulture = culture;
            CultureInfo.DefaultThreadCurrentCulture = culture;
            CultureInfo.DefaultThreadCurrentUICulture = culture;

            // تحديث اتجاه النص
            // Update text direction
            var isRtl = languageCode.StartsWith("ar");
            if (System.Windows.Application.Current?.MainWindow != null)
            {
                System.Windows.Application.Current.MainWindow.FlowDirection = 
                    isRtl ? System.Windows.FlowDirection.RightToLeft : System.Windows.FlowDirection.LeftToRight;
            }

            LanguageChanged?.Invoke(this, languageCode);
            _logger.LogInformation("Language changed to: {LanguageCode}", languageCode);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to set language: {LanguageCode}", languageCode);
        }
    }

    public string GetCurrentLanguage()
    {
        return _currentLanguage;
    }

    public string GetLocalizedString(string key)
    {
        try
        {
            if (_localizedStrings.TryGetValue(_currentLanguage, out var languageStrings) &&
                languageStrings.TryGetValue(key, out var localizedString))
            {
                return localizedString;
            }

            // العودة للإنجليزية كلغة افتراضية
            // Fallback to English as default
            if (_currentLanguage != "en-US" &&
                _localizedStrings.TryGetValue("en-US", out var englishStrings) &&
                englishStrings.TryGetValue(key, out var englishString))
            {
                return englishString;
            }

            _logger.LogWarning("Localized string not found for key: {Key}", key);
            return $"[{key}]";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get localized string for key: {Key}", key);
            return $"[{key}]";
        }
    }

    public string GetLocalizedString(string key, params object[] args)
    {
        try
        {
            var format = GetLocalizedString(key);
            return string.Format(format, args);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to format localized string for key: {Key}", key);
            return $"[{key}]";
        }
    }

    public Dictionary<string, string> GetSupportedLanguages()
    {
        return new Dictionary<string, string>(_supportedLanguages);
    }

    public bool IsLanguageSupported(string languageCode)
    {
        return _supportedLanguages.ContainsKey(languageCode);
    }

    private Dictionary<string, Dictionary<string, string>> InitializeLocalizedStrings()
    {
        return new Dictionary<string, Dictionary<string, string>>
        {
            {
                "ar-SA", new Dictionary<string, string>
                {
                    // واجهة المستخدم الرئيسية
                    { "AppTitle", "محرر الأشرافي" },
                    { "Library", "المكتبة" },
                    { "Edit", "التحرير" },
                    { "Export", "التصدير" },
                    { "Settings", "الإعدادات" },
                    { "Ready", "جاهز" },
                    { "Loading", "جاري التحميل..." },
                    { "ImportPhotos", "استيراد الصور" },
                    { "SelectImages", "اختر الصور" },
                    { "Welcome", "مرحباً بك في محرر الأشرافي" },
                    
                    // أدوات التحرير
                    { "Exposure", "التعرض" },
                    { "Contrast", "التباين" },
                    { "Saturation", "التشبع" },
                    { "Sharpness", "الحدة" },
                    { "Shadows", "الظلال" },
                    { "Highlights", "الإضاءات العالية" },
                    { "Brightness", "السطوع" },
                    { "Vibrance", "الحيوية" },
                    { "Clarity", "الوضوح" },
                    { "Temperature", "درجة الحرارة" },
                    { "Tint", "الصبغة" },
                    
                    // رسائل الحالة
                    { "PhotosImported", "تم استيراد الصور بنجاح" },
                    { "ImportFailed", "فشل في استيراد الصور" },
                    { "PhotoSelected", "تم اختيار الصورة: {0}" },
                    { "ThemeChanged", "تم تغيير الثيم" },
                    { "LanguageChanged", "تم تغيير اللغة" },
                    
                    // أزرار وعناصر التحكم
                    { "OK", "موافق" },
                    { "Cancel", "إلغاء" },
                    { "Apply", "تطبيق" },
                    { "Reset", "إعادة تعيين" },
                    { "Save", "حفظ" },
                    { "Open", "فتح" },
                    { "Close", "إغلاق" },
                    { "Minimize", "تصغير" },
                    { "Maximize", "تكبير" },
                    
                    // رسائل الخطأ
                    { "Error", "خطأ" },
                    { "Warning", "تحذير" },
                    { "Information", "معلومات" },
                    { "UnexpectedError", "حدث خطأ غير متوقع" },
                    { "FileNotFound", "الملف غير موجود" },
                    { "AccessDenied", "تم رفض الوصول" },
                    { "InvalidFormat", "تنسيق غير صالح" }
                }
            },
            {
                "en-US", new Dictionary<string, string>
                {
                    // Main UI
                    { "AppTitle", "ELashrafy Editor" },
                    { "Library", "Library" },
                    { "Edit", "Edit" },
                    { "Export", "Export" },
                    { "Settings", "Settings" },
                    { "Ready", "Ready" },
                    { "Loading", "Loading..." },
                    { "ImportPhotos", "Import Photos" },
                    { "SelectImages", "Select Images" },
                    { "Welcome", "Welcome to ELashrafy Editor" },
                    
                    // Editing Tools
                    { "Exposure", "Exposure" },
                    { "Contrast", "Contrast" },
                    { "Saturation", "Saturation" },
                    { "Sharpness", "Sharpness" },
                    { "Shadows", "Shadows" },
                    { "Highlights", "Highlights" },
                    { "Brightness", "Brightness" },
                    { "Vibrance", "Vibrance" },
                    { "Clarity", "Clarity" },
                    { "Temperature", "Temperature" },
                    { "Tint", "Tint" },
                    
                    // Status Messages
                    { "PhotosImported", "Photos imported successfully" },
                    { "ImportFailed", "Failed to import photos" },
                    { "PhotoSelected", "Photo selected: {0}" },
                    { "ThemeChanged", "Theme changed" },
                    { "LanguageChanged", "Language changed" },
                    
                    // Buttons and Controls
                    { "OK", "OK" },
                    { "Cancel", "Cancel" },
                    { "Apply", "Apply" },
                    { "Reset", "Reset" },
                    { "Save", "Save" },
                    { "Open", "Open" },
                    { "Close", "Close" },
                    { "Minimize", "Minimize" },
                    { "Maximize", "Maximize" },
                    
                    // Error Messages
                    { "Error", "Error" },
                    { "Warning", "Warning" },
                    { "Information", "Information" },
                    { "UnexpectedError", "An unexpected error occurred" },
                    { "FileNotFound", "File not found" },
                    { "AccessDenied", "Access denied" },
                    { "InvalidFormat", "Invalid format" }
                }
            }
        };
    }
}
