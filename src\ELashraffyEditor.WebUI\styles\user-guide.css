/* دليل المستخدم التفاعلي */
/* Interactive User Guide Styles */

.guide-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background-color: var(--primary-bg);
    color: var(--primary-text);
}

/* Header */
.guide-header {
    background-color: var(--secondary-bg);
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-md);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.logo .material-icons {
    font-size: 32px;
    color: var(--accent-color);
}

.logo h1 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
}

.logo .subtitle {
    font-size: 14px;
    color: var(--secondary-text);
    font-family: var(--font-english);
}

.header-controls {
    display: flex;
    gap: var(--spacing-sm);
}

/* Search Bar */
.search-bar {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-md);
    padding: var(--spacing-sm);
    background-color: var(--tertiary-bg);
    border-radius: var(--radius-md);
    transition: all var(--transition-normal);
}

.search-bar.hidden {
    display: none;
}

.search-bar input {
    flex: 1;
    padding: var(--spacing-sm);
    border: none;
    background: transparent;
    color: var(--primary-text);
    font-size: 14px;
}

.search-bar input:focus {
    outline: none;
}

.search-bar input::placeholder {
    color: var(--tertiary-text);
}

/* Content Layout */
.guide-content {
    display: flex;
    flex: 1;
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
}

/* Sidebar */
.guide-sidebar {
    width: 280px;
    background-color: var(--secondary-bg);
    border-right: 1px solid var(--border-color);
    padding: var(--spacing-lg);
    overflow-y: auto;
    position: sticky;
    top: 80px;
    height: calc(100vh - 80px);
}

.sidebar-content h3 {
    color: var(--accent-color);
    margin-bottom: var(--spacing-md);
    font-size: 16px;
}

.guide-nav {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-item {
    margin-bottom: var(--spacing-xs);
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--secondary-text);
    text-decoration: none;
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
    font-size: 14px;
}

.nav-link:hover {
    background-color: var(--tertiary-bg);
    color: var(--primary-text);
}

.nav-item.active .nav-link {
    background-color: var(--accent-color);
    color: white;
}

.nav-link .material-icons {
    font-size: 18px;
}

/* Main Content */
.guide-main {
    flex: 1;
    padding: var(--spacing-xl);
    overflow-y: auto;
}

.guide-section {
    display: none;
    animation: fadeIn var(--transition-normal);
}

.guide-section.active {
    display: block;
}

.guide-section h2 {
    color: var(--accent-color);
    margin-bottom: var(--spacing-lg);
    font-size: 28px;
    border-bottom: 2px solid var(--accent-color);
    padding-bottom: var(--spacing-sm);
}

/* Introduction Card */
.intro-card {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-xl);
    background-color: var(--secondary-bg);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
    border: 1px solid var(--border-color);
}

.intro-content h3 {
    color: var(--accent-color);
    margin-bottom: var(--spacing-sm);
    font-size: 24px;
}

.intro-content h4 {
    color: var(--secondary-text);
    margin-bottom: var(--spacing-md);
    font-size: 18px;
    font-family: var(--font-english);
}

.intro-content p {
    margin-bottom: var(--spacing-sm);
    line-height: 1.6;
}

.intro-video {
    display: flex;
    align-items: center;
    justify-content: center;
}

.video-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: var(--tertiary-bg);
    border-radius: var(--radius-md);
    padding: var(--spacing-xl);
    text-align: center;
    cursor: pointer;
    transition: all var(--transition-normal);
    min-height: 200px;
}

.video-placeholder:hover {
    background-color: var(--quaternary-bg);
    transform: scale(1.02);
}

.video-placeholder .material-icons {
    font-size: 64px;
    color: var(--accent-color);
    margin-bottom: var(--spacing-md);
}

/* Steps Container */
.steps-container {
    margin-bottom: var(--spacing-xl);
}

.steps-container h3 {
    color: var(--accent-color);
    margin-bottom: var(--spacing-lg);
    font-size: 20px;
}

.step-card {
    display: flex;
    gap: var(--spacing-lg);
    background-color: var(--secondary-bg);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
    border: 1px solid var(--border-color);
    transition: all var(--transition-normal);
}

.step-card:hover {
    border-color: var(--accent-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.step-number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    background-color: var(--accent-color);
    color: white;
    border-radius: 50%;
    font-size: 20px;
    font-weight: bold;
    flex-shrink: 0;
}

.step-content {
    flex: 1;
}

.step-content h4 {
    color: var(--primary-text);
    margin-bottom: var(--spacing-sm);
    font-size: 18px;
}

.step-content p {
    color: var(--secondary-text);
    margin-bottom: var(--spacing-md);
    line-height: 1.5;
}

.step-actions {
    display: flex;
    gap: var(--spacing-sm);
}

/* Tips Section */
.tips-section {
    margin-bottom: var(--spacing-xl);
}

.tips-section h3 {
    color: var(--accent-color);
    margin-bottom: var(--spacing-lg);
    font-size: 20px;
}

.tips-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

.tip-card {
    background-color: var(--secondary-bg);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    text-align: center;
    border: 1px solid var(--border-color);
    transition: all var(--transition-normal);
}

.tip-card:hover {
    border-color: var(--accent-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.tip-card .material-icons {
    font-size: 48px;
    color: var(--accent-color);
    margin-bottom: var(--spacing-md);
}

.tip-card h4 {
    color: var(--primary-text);
    margin-bottom: var(--spacing-sm);
    font-size: 16px;
}

.tip-card p {
    color: var(--secondary-text);
    font-size: 14px;
    line-height: 1.4;
}

/* Interface Overview */
.interface-overview {
    margin-bottom: var(--spacing-xl);
}

.interface-image {
    position: relative;
    background-color: var(--secondary-bg);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    text-align: center;
    border: 1px solid var(--border-color);
}

.interface-image img {
    max-width: 100%;
    height: auto;
    border-radius: var(--radius-sm);
}

.interface-hotspots {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.hotspot {
    position: absolute;
    cursor: pointer;
}

.hotspot-marker {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background-color: var(--accent-color);
    color: white;
    border-radius: 50%;
    font-weight: bold;
    font-size: 14px;
    animation: pulse 2s infinite;
}

.hotspot-tooltip {
    position: absolute;
    top: 40px;
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--primary-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    padding: var(--spacing-md);
    min-width: 200px;
    box-shadow: var(--shadow-lg);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
    z-index: 10;
}

.hotspot:hover .hotspot-tooltip {
    opacity: 1;
    visibility: visible;
}

.hotspot-tooltip h4 {
    color: var(--accent-color);
    margin-bottom: var(--spacing-xs);
    font-size: 14px;
}

.hotspot-tooltip p {
    color: var(--secondary-text);
    font-size: 12px;
    margin: 0;
}

/* Interface Details */
.interface-details h3 {
    color: var(--accent-color);
    margin-bottom: var(--spacing-lg);
    font-size: 20px;
}

.component-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.component-item {
    display: flex;
    gap: var(--spacing-md);
    background-color: var(--secondary-bg);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    border: 1px solid var(--border-color);
    transition: all var(--transition-normal);
}

.component-item:hover {
    border-color: var(--accent-color);
    transform: translateX(5px);
}

.component-item .material-icons {
    font-size: 32px;
    color: var(--accent-color);
    flex-shrink: 0;
}

.component-content h4 {
    color: var(--primary-text);
    margin-bottom: var(--spacing-sm);
    font-size: 16px;
}

.component-content p {
    color: var(--secondary-text);
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: var(--spacing-xs);
}

/* Progress Indicator */
.progress-indicator {
    position: fixed;
    bottom: 20px;
    right: 20px;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    background-color: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-sm) var(--spacing-md);
    box-shadow: var(--shadow-lg);
    z-index: 50;
}

.progress-bar {
    width: 100px;
    height: 6px;
    background-color: var(--tertiary-bg);
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background-color: var(--accent-color);
    border-radius: 3px;
    transition: width var(--transition-normal);
    width: 0%;
}

.progress-text {
    font-size: 12px;
    color: var(--secondary-text);
    font-weight: 500;
    min-width: 30px;
}

/* Animations */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .guide-content {
        flex-direction: column;
    }
    
    .guide-sidebar {
        width: 100%;
        height: auto;
        position: static;
        border-right: none;
        border-bottom: 1px solid var(--border-color);
    }
    
    .intro-card {
        grid-template-columns: 1fr;
    }
    
    .tips-grid {
        grid-template-columns: 1fr;
    }
    
    .progress-indicator {
        bottom: 10px;
        right: 10px;
        left: 10px;
        justify-content: center;
    }
}
