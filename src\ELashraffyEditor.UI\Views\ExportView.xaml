<UserControl x:Class="ELashraffyEditor.UI.Views.ExportView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:viewmodels="clr-namespace:ELashraffyEditor.UI.ViewModels"
             mc:Ignorable="d" 
             d:DesignHeight="800" d:DesignWidth="1200"
             d:DataContext="{d:DesignInstance Type=viewmodels:ExportViewModel}">

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="300"/>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="300"/>
        </Grid.ColumnDefinitions>

        <!-- قائمة الصور للتصدير / Export Photos List -->
        <materialDesign:Card Grid.Column="0" Margin="10" Padding="15">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <TextBlock Grid.Row="0" Text="الصور للتصدير / Photos to Export" 
                           Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                           Margin="0,0,0,15"/>

                <ListBox Grid.Row="1" ItemsSource="{Binding PhotosToExport}"
                         ScrollViewer.HorizontalScrollBarVisibility="Disabled">
                    <ListBox.ItemTemplate>
                        <DataTemplate>
                            <Grid Margin="0,5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="50"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <!-- صورة مصغرة / Thumbnail -->
                                <Border Grid.Column="0" Width="40" Height="40" 
                                        Background="{DynamicResource MaterialDesignDivider}"
                                        CornerRadius="4">
                                    <Image Source="{Binding ThumbnailData}" Stretch="UniformToFill"/>
                                </Border>

                                <!-- معلومات الصورة / Photo Info -->
                                <StackPanel Grid.Column="1" Margin="10,0">
                                    <TextBlock Text="{Binding FileName}" FontWeight="Medium" FontSize="12"/>
                                    <TextBlock Text="{Binding Width, StringFormat='{}{0} x '}" FontSize="10" Opacity="0.7"/>
                                </StackPanel>

                                <!-- زر الحذف / Remove Button -->
                                <Button Grid.Column="2" 
                                        Command="{Binding DataContext.RemovePhotoCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                        CommandParameter="{Binding}"
                                        Style="{StaticResource MaterialDesignIconButton}"
                                        Width="24" Height="24">
                                    <materialDesign:PackIcon Kind="Close" Width="16" Height="16"/>
                                </Button>
                            </Grid>
                        </DataTemplate>
                    </ListBox.ItemTemplate>
                </ListBox>

                <StackPanel Grid.Row="2" Margin="0,10,0,0">
                    <TextBlock Text="{Binding TotalFiles, StringFormat='المجموع / Total: {0}'}" 
                               HorizontalAlignment="Center" FontSize="12" Opacity="0.7"/>
                    <Button Content="مسح الكل / Clear All" 
                            Command="{Binding ClearPhotosCommand}"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Margin="0,5"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- إعدادات التصدير / Export Settings -->
        <materialDesign:Card Grid.Column="1" Margin="10" Padding="20">
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <StackPanel>
                    <TextBlock Text="إعدادات التصدير / Export Settings" 
                               Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                               Margin="0,0,0,20"/>

                    <!-- مجلد الإخراج / Output Folder -->
                    <materialDesign:Card Padding="15" Margin="0,0,0,15">
                        <StackPanel>
                            <TextBlock Text="مجلد الإخراج / Output Folder" 
                                       Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                       Margin="0,0,0,10"/>
                            
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <TextBox Grid.Column="0" Text="{Binding OutputFolder, Mode=TwoWay}"
                                         materialDesign:HintAssist.Hint="اختر مجلد الحفظ / Select save folder"
                                         Style="{StaticResource MaterialDesignOutlinedTextBox}"/>
                                
                                <Button Grid.Column="1" Content="تصفح / Browse"
                                        Command="{Binding SelectOutputFolderCommand}"
                                        Style="{StaticResource MaterialDesignRaisedButton}"
                                        Margin="10,0,0,0"/>
                            </Grid>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- تنسيق الملف / File Format -->
                    <materialDesign:Card Padding="15" Margin="0,0,0,15">
                        <StackPanel>
                            <TextBlock Text="تنسيق الملف / File Format" 
                                       Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                       Margin="0,0,0,10"/>
                            
                            <ComboBox ItemsSource="{Binding AvailableFormats}"
                                      SelectedItem="{Binding SelectedFormat, Mode=TwoWay}"
                                      materialDesign:HintAssist.Hint="اختر التنسيق / Select format"
                                      Style="{StaticResource MaterialDesignOutlinedComboBox}"/>
                            
                            <!-- جودة JPEG / JPEG Quality -->
                            <StackPanel Visibility="{Binding SelectedFormat, Converter={StaticResource StringToVisibilityConverter}, ConverterParameter='JPEG'}">
                                <TextBlock Text="الجودة / Quality" Margin="0,15,0,5"/>
                                <Slider Value="{Binding Quality, Mode=TwoWay}"
                                        Minimum="50" Maximum="100"
                                        TickFrequency="10" IsSnapToTickEnabled="True"
                                        Style="{StaticResource MaterialDesignSlider}"/>
                                <TextBlock Text="{Binding Quality, StringFormat='{}{0}%'}" 
                                           HorizontalAlignment="Center" FontSize="12"/>
                            </StackPanel>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- تغيير الحجم / Resize Options -->
                    <materialDesign:Card Padding="15" Margin="0,0,0,15">
                        <StackPanel>
                            <CheckBox Content="تغيير حجم الصور / Resize Images"
                                      IsChecked="{Binding ResizeImages, Mode=TwoWay}"
                                      Style="{StaticResource MaterialDesignCheckBox}"
                                      Margin="0,0,0,10"/>
                            
                            <Grid IsEnabled="{Binding ResizeImages}">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <TextBox Grid.Column="0" 
                                         Text="{Binding MaxWidth, Mode=TwoWay}"
                                         materialDesign:HintAssist.Hint="العرض / Width"
                                         Style="{StaticResource MaterialDesignOutlinedTextBox}"/>
                                
                                <TextBlock Grid.Column="1" Text="×" 
                                           VerticalAlignment="Center" Margin="10"/>
                                
                                <TextBox Grid.Column="2" 
                                         Text="{Binding MaxHeight, Mode=TwoWay}"
                                         materialDesign:HintAssist.Hint="الارتفاع / Height"
                                         Style="{StaticResource MaterialDesignOutlinedTextBox}"/>
                            </Grid>
                            
                            <CheckBox Content="الحفاظ على النسبة / Maintain Aspect Ratio"
                                      IsChecked="{Binding MaintainAspectRatio, Mode=TwoWay}"
                                      Style="{StaticResource MaterialDesignCheckBox}"
                                      Margin="0,10,0,0"
                                      IsEnabled="{Binding ResizeImages}"/>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- تسمية الملفات / File Naming -->
                    <materialDesign:Card Padding="15" Margin="0,0,0,15">
                        <StackPanel>
                            <TextBlock Text="تسمية الملفات / File Naming" 
                                       Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                       Margin="0,0,0,10"/>
                            
                            <ComboBox ItemsSource="{Binding FileNamePatterns}"
                                      SelectedItem="{Binding FileNamePattern, Mode=TwoWay}"
                                      materialDesign:HintAssist.Hint="نمط التسمية / Naming pattern"
                                      Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                      IsEditable="True"/>
                            
                            <TextBlock Text="مثال / Example: photo_001_exported.jpg" 
                                       FontSize="11" Opacity="0.7" Margin="0,5,0,0"/>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- العلامة المائية / Watermark -->
                    <materialDesign:Card Padding="15" Margin="0,0,0,15">
                        <StackPanel>
                            <CheckBox Content="إضافة علامة مائية / Add Watermark"
                                      IsChecked="{Binding AddWatermark, Mode=TwoWay}"
                                      Style="{StaticResource MaterialDesignCheckBox}"
                                      Margin="0,0,0,10"/>
                            
                            <TextBox Text="{Binding WatermarkText, Mode=TwoWay}"
                                     materialDesign:HintAssist.Hint="نص العلامة المائية / Watermark text"
                                     Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                     IsEnabled="{Binding AddWatermark}"/>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- البيانات الوصفية / Metadata -->
                    <materialDesign:Card Padding="15" Margin="0,0,0,15">
                        <StackPanel>
                            <CheckBox Content="الحفاظ على البيانات الوصفية / Preserve Metadata"
                                      IsChecked="{Binding PreserveMetadata, Mode=TwoWay}"
                                      Style="{StaticResource MaterialDesignCheckBox}"/>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- أزرار الإجراءات / Action Buttons -->
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
                        <Button Content="حفظ إعداد مسبق / Save Preset"
                                Command="{Binding SaveExportPresetCommand}"
                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                Margin="5"/>
                        
                        <Button Content="بدء التصدير / Start Export"
                                Command="{Binding StartExportCommand}"
                                Style="{StaticResource MaterialDesignRaisedButton}"
                                Margin="5"
                                IsEnabled="{Binding CanStartExport}"/>
                    </StackPanel>
                </StackPanel>
            </ScrollViewer>
        </materialDesign:Card>

        <!-- التقدم والمعاينة / Progress and Preview -->
        <materialDesign:Card Grid.Column="2" Margin="10" Padding="15">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <TextBlock Grid.Row="0" Text="التقدم / Progress" 
                           Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                           Margin="0,0,0,15"/>

                <!-- شريط التقدم / Progress Bar -->
                <StackPanel Grid.Row="1">
                    <ProgressBar Value="{Binding ExportProgress}"
                                 Maximum="100"
                                 Style="{StaticResource MaterialDesignLinearProgressBar}"
                                 Height="8"
                                 Margin="0,0,0,10"/>
                    
                    <TextBlock Text="{Binding ExportProgress, StringFormat='{}{0:F0}%'}"
                               HorizontalAlignment="Center"
                               FontSize="14" FontWeight="Medium"/>
                    
                    <TextBlock Text="{Binding ExportStatus}"
                               HorizontalAlignment="Center"
                               FontSize="12" Opacity="0.7"
                               Margin="0,5,0,0"
                               TextWrapping="Wrap"/>

                    <!-- إحصائيات التصدير / Export Statistics -->
                    <materialDesign:Card Padding="10" Margin="0,20,0,0"
                                         Visibility="{Binding IsExporting, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <StackPanel>
                            <TextBlock Text="الإحصائيات / Statistics" 
                                       Style="{StaticResource MaterialDesignSubtitle2TextBlock}"
                                       Margin="0,0,0,10"/>
                            
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="المجموع / Total:" Margin="0,2"/>
                                <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding TotalFiles}" 
                                           HorizontalAlignment="Right" Margin="5,2"/>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="مكتمل / Completed:" Margin="0,2"/>
                                <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding ProcessedFiles}" 
                                           HorizontalAlignment="Right" Margin="5,2"/>

                                <TextBlock Grid.Row="2" Grid.Column="0" Text="فشل / Failed:" Margin="0,2"/>
                                <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding FailedFiles}" 
                                           HorizontalAlignment="Right" Margin="5,2"
                                           Foreground="{DynamicResource MaterialDesignValidationErrorBrush}"/>
                            </Grid>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- معاينة الإعدادات / Settings Preview -->
                    <materialDesign:Card Padding="10" Margin="0,20,0,0">
                        <StackPanel>
                            <TextBlock Text="معاينة الإعدادات / Settings Preview" 
                                       Style="{StaticResource MaterialDesignSubtitle2TextBlock}"
                                       Margin="0,0,0,10"/>
                            
                            <TextBlock FontSize="11" Opacity="0.8">
                                <Run Text="التنسيق / Format:"/>
                                <Run Text="{Binding SelectedFormat}" FontWeight="Medium"/>
                            </TextBlock>
                            
                            <TextBlock FontSize="11" Opacity="0.8" Margin="0,2,0,0">
                                <Run Text="الجودة / Quality:"/>
                                <Run Text="{Binding Quality, StringFormat='{}{0}%'}" FontWeight="Medium"/>
                            </TextBlock>
                            
                            <TextBlock FontSize="11" Opacity="0.8" Margin="0,2,0,0"
                                       Visibility="{Binding ResizeImages, Converter={StaticResource BooleanToVisibilityConverter}}">
                                <Run Text="الحجم / Size:"/>
                                <Run Text="{Binding MaxWidth}" FontWeight="Medium"/>
                                <Run Text="×"/>
                                <Run Text="{Binding MaxHeight}" FontWeight="Medium"/>
                            </TextBlock>
                        </StackPanel>
                    </materialDesign:Card>
                </StackPanel>

                <!-- أزرار التحكم / Control Buttons -->
                <StackPanel Grid.Row="2" Margin="0,15,0,0">
                    <Button Content="إيقاف مؤقت / Pause"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Margin="0,5"
                            Visibility="{Binding IsExporting, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                    
                    <Button Content="إلغاء / Cancel"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Margin="0,5"
                            Visibility="{Binding IsExporting, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>
    </Grid>
</UserControl>
