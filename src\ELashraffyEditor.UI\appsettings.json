{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "ELashraffyEditor": "Debug"}}, "Application": {"Name": "ELashrafy Editor", "Version": "1.0.0", "Company": "ELashrafy", "Copyright": "Copyright © ELashrafy 2024"}, "UI": {"DefaultTheme": "Dark", "DefaultLanguage": "ar-SA", "WindowSettings": {"DefaultWidth": 1400, "DefaultHeight": 900, "MinWidth": 1000, "MinHeight": 600, "StartupLocation": "CenterScreen", "DefaultState": "Maximized"}, "Animation": {"EnableAnimations": true, "FastDuration": 150, "NormalDuration": 250, "SlowDuration": 400}}, "ImageProcessing": {"SupportedFormats": [".jpg", ".jpeg", ".png", ".tiff", ".tif", ".bmp", ".gif", ".webp"], "RawFormats": [".cr2", ".nef", ".arw", ".dng", ".raf", ".orf", ".rw2"], "ThumbnailSettings": {"MaxWidth": 200, "MaxHeight": 200, "Quality": 85, "CacheEnabled": true, "CacheDirectory": "Thumbnails"}, "ExportSettings": {"DefaultFormat": "JPEG", "DefaultQuality": 95, "PreserveMetadata": true, "ColorProfile": "sRGB"}, "Performance": {"EnableGPUAcceleration": true, "MaxMemoryUsage": 4096, "ThreadCount": 0, "EnableParallelProcessing": true}}, "Scripting": {"Lua": {"Enabled": true, "ScriptsDirectory": "<PERSON><PERSON><PERSON>", "AllowFileAccess": false, "AllowNetworkAccess": false, "TimeoutSeconds": 30}, "Python": {"Enabled": true, "ScriptsDirectory": "PythonScripts", "VirtualEnvironment": "", "RequiredPackages": ["opencv-python", "numpy", "pillow", "scikit-image"]}}, "WebView": {"Enabled": true, "UserDataFolder": "WebView2Data", "AllowExternalNavigation": false, "EnableDevTools": false}, "Localization": {"SupportedLanguages": {"ar-SA": "العربية", "en-US": "English"}, "FallbackLanguage": "en-US", "ResourcesDirectory": "Resources/Localization"}, "AutoSave": {"Enabled": true, "IntervalMinutes": 5, "MaxBackups": 10, "BackupDirectory": "Backups"}, "RecentFiles": {"MaxCount": 20, "ShowInStartPage": true}, "Plugins": {"Enabled": true, "PluginsDirectory": "Plugins", "AllowThirdParty": false}, "Updates": {"CheckForUpdates": true, "UpdateChannel": "Stable", "AutoDownload": false}, "Telemetry": {"Enabled": false, "CollectUsageData": false, "CollectCrashReports": true}}