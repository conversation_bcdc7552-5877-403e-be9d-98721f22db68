<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <LangVersion>latest</LangVersion>
    <AssemblyTitle>ELashrafy Editor Scripting</AssemblyTitle>
    <AssemblyDescription>Lua and Python scripting support for ELashrafy Editor</AssemblyDescription>
    <AssemblyCompany>ELashrafy</AssemblyCompany>
    <AssemblyProduct>ELashrafy Editor</AssemblyProduct>
    <Copyright>Copyright © ELashrafy 2024</Copyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="NLua" Version="1.7.0" />
    <PackageReference Include="Python.Runtime" Version="2.7.9" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="7.0.0" />
    <PackageReference Include="System.Text.Json" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ELashraffyEditor.Core\ELashraffyEditor.Core.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Lua\" />
    <Folder Include="Python\" />
    <Folder Include="Scripts\" />
  </ItemGroup>

</Project>
