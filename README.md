# محرر الأشرافي / ELashrafy Editor

**محرر صور احترافي متقدم مع دعم متعدد التقنيات**  
**Professional Advanced Photo Editor with Multi-Technology Support**

![ELashrafy Editor](https://img.shields.io/badge/Version-1.0.0-blue)
![.NET](https://img.shields.io/badge/.NET-6.0-purple)
![License](https://img.shields.io/badge/License-MIT-green)

## 📋 نظرة عامة / Overview

محرر الأشرافي هو تطبيق تحرير صور احترافي مصمم ليكون بديلاً قوياً لـ Adobe Lightroom. يجمع التطبيق بين قوة C# WPF وحداثة تقنيات الويب مع أداء Rust العالي ومرونة Python للذكاء الاصطناعي.

ELashrafy Editor is a professional photo editing application designed to be a powerful alternative to Adobe Lightroom. The application combines the power of C# WPF with modern web technologies, high-performance Rust modules, and Python flexibility for AI features.

## ✨ المميزات الرئيسية / Key Features

### 🎨 واجهة المستخدم / User Interface
- **Material Design** مع دعم الثيم المظلم والفاتح
- **دعم ثنائي اللغة** (العربية والإنجليزية) مع RTL
- **واجهة حديثة** مع WebView2 للإعدادات
- **تصميم متجاوب** يتكيف مع أحجام الشاشات المختلفة

### 📸 معالجة الصور / Image Processing
- **دعم تنسيقات متعددة**: JPEG, PNG, TIFF, BMP, WebP
- **دعم RAW أساسي**: CR2, NEF, ARW, DNG
- **تعديلات غير مدمرة** مع طبقات التعديل
- **أدوات تحرير شاملة**: التعرض، التباين، التشبع، الحدة، وأكثر
- **هيستوجرام مباشر** ومقارنة قبل/بعد

### 🔧 التقنيات المتقدمة / Advanced Technologies

#### **C# .NET 6 WPF**
- نمط MVVM للهندسة النظيفة
- Material Design للواجهات الحديثة
- ImageSharp لمعالجة الصور الأساسية
- إدارة ذاكرة محسنة

#### **WebView2 Integration**
- صفحات إعدادات تفاعلية بـ HTML/CSS/JS
- واجهة مستخدم حديثة ومرنة
- تكامل سلس مع التطبيق الرئيسي

#### **Lua Scripting**
- إمكانية إنشاء سكريبتات مخصصة
- API شامل للعمليات على الصور
- بيئة آمنة ومحدودة للتنفيذ

#### **Python AI Integration**
- تحسين تلقائي ذكي للصور
- تصحيح الألوان بالذكاء الاصطناعي
- دعم OpenCV و scikit-image

#### **Rust Performance Modules**
- معالجة عالية الأداء للصور الكبيرة
- تقليل الضوضاء المتقدم
- عمليات محسنة للذاكرة

## 🏗️ هيكل المشروع / Project Structure

```
ELashraffyEditor/
├── src/
│   ├── ELashraffyEditor.Core/          # منطق العمل الأساسي
│   ├── ELashraffyEditor.UI/            # تطبيق WPF الرئيسي
│   ├── ELashraffyEditor.WebUI/         # واجهات HTML/CSS/JS
│   ├── ELashraffyEditor.Scripting/     # دعم Lua و Python
│   ├── native/
│   │   ├── rust-processing/            # وحدات Rust للأداء
│   │   └── python-ai/                  # سكريبتات Python للذكاء الاصطناعي
│   └── resources/                      # الموارد والترجمات
├── tests/                              # الاختبارات
└── docs/                               # التوثيق
```

## 🚀 البدء السريع / Quick Start

### المتطلبات / Requirements
- **Windows 10/11** (x64)
- **.NET 6 Runtime** أو أحدث
- **Visual Studio 2022** للتطوير
- **WebView2 Runtime** (يتم تثبيته تلقائياً)

### التثبيت / Installation

1. **استنساخ المستودع / Clone Repository**
```bash
git clone https://github.com/elashrafy/elashrafy-editor.git
cd elashrafy-editor
```

2. **بناء المشروع / Build Project**
```bash
dotnet restore
dotnet build --configuration Release
```

3. **تشغيل التطبيق / Run Application**
```bash
dotnet run --project src/ELashraffyEditor.UI
```

### إنشاء ملف EXE / Creating EXE File
```bash
dotnet publish src/ELashraffyEditor.UI -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true
```

## 🎯 الاستخدام / Usage

### 1. استيراد الصور / Import Photos
- اضغط على "استيراد الصور" في الشاشة الرئيسية
- اختر الصور أو المجلد المطلوب
- سيتم إنشاء صور مصغرة تلقائياً

### 2. تحرير الصور / Edit Photos
- اختر صورة من المكتبة
- انتقل إلى وضع "التحرير"
- استخدم أشرطة التمرير لتعديل الصورة
- شاهد التغييرات مباشرة مع الهيستوجرام

### 3. التصدير / Export
- انتقل إلى وضع "التصدير"
- اختر التنسيق والجودة المطلوبة
- حدد مجلد الحفظ وابدأ التصدير

### 4. الإعدادات / Settings
- انتقل إلى "الإعدادات" لتخصيص التطبيق
- غير اللغة والثيم حسب تفضيلك
- اضبط إعدادات الأداء والذاكرة

## 🔧 التطوير / Development

### إضافة ميزات جديدة / Adding New Features

#### إضافة فلتر جديد / Adding New Filter
```csharp
// في ImageProcessingService
public async Task<Image<Rgba32>?> ApplyCustomFilterAsync(Image<Rgba32> image, string filterName)
{
    return filterName switch
    {
        "myFilter" => await ApplyMyFilterAsync(image),
        _ => image
    };
}
```

#### إضافة سكريبت Lua / Adding Lua Script
```lua
-- مثال على سكريبت تحسين الصورة
function enhanceImage(image)
    image:adjustBrightness(0.1)
    image:adjustContrast(0.2)
    return image
end
```

#### إضافة وحدة Python / Adding Python Module
```python
import cv2
import numpy as np

def auto_enhance(image_path):
    """تحسين تلقائي للصورة باستخدام OpenCV"""
    image = cv2.imread(image_path)
    # منطق التحسين هنا
    return enhanced_image
```

## 📦 التبعيات / Dependencies

### C# Packages
- **MaterialDesignThemes** - واجهة Material Design
- **Microsoft.Web.WebView2** - تكامل الويب
- **SixLabors.ImageSharp** - معالجة الصور
- **CommunityToolkit.Mvvm** - نمط MVVM
- **NLua** - دعم Lua
- **Python.Runtime** - تكامل Python

### Web Technologies
- **HTML5/CSS3/JavaScript** - واجهات الإعدادات
- **Material Icons** - الأيقونات
- **Google Fonts** - الخطوط

## 🌍 الترجمة / Localization

التطبيق يدعم حالياً:
- **العربية** (ar-SA) مع دعم RTL
- **الإنجليزية** (en-US)

لإضافة لغة جديدة، أضف الترجمات في `LocalizationService.cs`.

## 🤝 المساهمة / Contributing

نرحب بالمساهمات! يرجى:

1. عمل Fork للمستودع
2. إنشاء فرع للميزة الجديدة
3. إجراء التغييرات مع الاختبارات
4. إرسال Pull Request

## 📄 الترخيص / License

هذا المشروع مرخص تحت رخصة MIT. راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 🆘 الدعم / Support

- **الإبلاغ عن الأخطاء**: [GitHub Issues](https://github.com/elashrafy/elashrafy-editor/issues)
- **الأسئلة**: [GitHub Discussions](https://github.com/elashrafy/elashrafy-editor/discussions)
- **التوثيق**: [Wiki](https://github.com/elashrafy/elashrafy-editor/wiki)

## 🙏 شكر وتقدير / Acknowledgments

- **Material Design** لتصميم الواجهات الجميلة
- **ImageSharp** لمعالجة الصور القوية
- **WebView2** للتكامل الحديث مع الويب
- **المجتمع المفتوح المصدر** للمكتبات والأدوات الرائعة

---

**صنع بـ ❤️ في المملكة العربية السعودية**  
**Made with ❤️ in Saudi Arabia**
