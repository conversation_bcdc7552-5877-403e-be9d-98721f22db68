namespace ELashraffyEditor.UI.Services;

/// <summary>
/// واجهة خدمة الترجمة والتوطين
/// Localization service interface
/// </summary>
public interface ILocalizationService
{
    /// <summary>
    /// تعيين اللغة الحالية
    /// Set current language
    /// </summary>
    void SetLanguage(string languageCode);

    /// <summary>
    /// الحصول على اللغة الحالية
    /// Get current language
    /// </summary>
    string GetCurrentLanguage();

    /// <summary>
    /// الحصول على النص المترجم
    /// Get localized text
    /// </summary>
    string GetLocalizedString(string key);

    /// <summary>
    /// الحصول على النص المترجم مع معاملات
    /// Get localized text with parameters
    /// </summary>
    string GetLocalizedString(string key, params object[] args);

    /// <summary>
    /// الحصول على اللغات المدعومة
    /// Get supported languages
    /// </summary>
    Dictionary<string, string> GetSupportedLanguages();

    /// <summary>
    /// التحقق من دعم اللغة
    /// Check if language is supported
    /// </summary>
    bool IsLanguageSupported(string languageCode);

    /// <summary>
    /// حدث تغيير اللغة
    /// Language changed event
    /// </summary>
    event EventHandler<string>? LanguageChanged;
}
