<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  
  <!-- Application Title -->
  <data name="AppTitle" xml:space="preserve">
    <value>ELashrafy Editor</value>
  </data>
  <data name="AppSubtitle" xml:space="preserve">
    <value>Professional Photo Editor</value>
  </data>
  
  <!-- Menu Items -->
  <data name="Menu_File" xml:space="preserve">
    <value>File</value>
  </data>
  <data name="Menu_Edit" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="Menu_View" xml:space="preserve">
    <value>View</value>
  </data>
  <data name="Menu_Tools" xml:space="preserve">
    <value>Tools</value>
  </data>
  <data name="Menu_Help" xml:space="preserve">
    <value>Help</value>
  </data>
  
  <!-- File Menu -->
  <data name="File_New" xml:space="preserve">
    <value>New</value>
  </data>
  <data name="File_Open" xml:space="preserve">
    <value>Open</value>
  </data>
  <data name="File_Save" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="File_SaveAs" xml:space="preserve">
    <value>Save As</value>
  </data>
  <data name="File_Import" xml:space="preserve">
    <value>Import Photos</value>
  </data>
  <data name="File_Export" xml:space="preserve">
    <value>Export</value>
  </data>
  <data name="File_Exit" xml:space="preserve">
    <value>Exit</value>
  </data>
  
  <!-- Edit Menu -->
  <data name="Edit_Undo" xml:space="preserve">
    <value>Undo</value>
  </data>
  <data name="Edit_Redo" xml:space="preserve">
    <value>Redo</value>
  </data>
  <data name="Edit_Copy" xml:space="preserve">
    <value>Copy</value>
  </data>
  <data name="Edit_Paste" xml:space="preserve">
    <value>Paste</value>
  </data>
  <data name="Edit_Reset" xml:space="preserve">
    <value>Reset</value>
  </data>
  
  <!-- View Menu -->
  <data name="View_Library" xml:space="preserve">
    <value>Library</value>
  </data>
  <data name="View_Develop" xml:space="preserve">
    <value>Develop</value>
  </data>
  <data name="View_Export" xml:space="preserve">
    <value>Export</value>
  </data>
  <data name="View_Fullscreen" xml:space="preserve">
    <value>Fullscreen</value>
  </data>
  <data name="View_ZoomIn" xml:space="preserve">
    <value>Zoom In</value>
  </data>
  <data name="View_ZoomOut" xml:space="preserve">
    <value>Zoom Out</value>
  </data>
  <data name="View_ZoomFit" xml:space="preserve">
    <value>Fit to Screen</value>
  </data>
  
  <!-- Editing Tools -->
  <data name="Tool_Exposure" xml:space="preserve">
    <value>Exposure</value>
  </data>
  <data name="Tool_Brightness" xml:space="preserve">
    <value>Brightness</value>
  </data>
  <data name="Tool_Contrast" xml:space="preserve">
    <value>Contrast</value>
  </data>
  <data name="Tool_Saturation" xml:space="preserve">
    <value>Saturation</value>
  </data>
  <data name="Tool_Vibrance" xml:space="preserve">
    <value>Vibrance</value>
  </data>
  <data name="Tool_Shadows" xml:space="preserve">
    <value>Shadows</value>
  </data>
  <data name="Tool_Highlights" xml:space="preserve">
    <value>Highlights</value>
  </data>
  <data name="Tool_Clarity" xml:space="preserve">
    <value>Clarity</value>
  </data>
  <data name="Tool_Sharpness" xml:space="preserve">
    <value>Sharpness</value>
  </data>
  
  <!-- Buttons -->
  <data name="Button_OK" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="Button_Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="Button_Apply" xml:space="preserve">
    <value>Apply</value>
  </data>
  <data name="Button_Reset" xml:space="preserve">
    <value>Reset</value>
  </data>
  <data name="Button_Browse" xml:space="preserve">
    <value>Browse</value>
  </data>
  <data name="Button_AutoEnhance" xml:space="preserve">
    <value>Auto Enhance</value>
  </data>
  
  <!-- Status Messages -->
  <data name="Status_Ready" xml:space="preserve">
    <value>Ready</value>
  </data>
  <data name="Status_Loading" xml:space="preserve">
    <value>Loading...</value>
  </data>
  <data name="Status_Processing" xml:space="preserve">
    <value>Processing...</value>
  </data>
  <data name="Status_Saving" xml:space="preserve">
    <value>Saving...</value>
  </data>
  <data name="Status_Complete" xml:space="preserve">
    <value>Complete</value>
  </data>
  
  <!-- Error Messages -->
  <data name="Error_FileNotFound" xml:space="preserve">
    <value>File not found</value>
  </data>
  <data name="Error_UnsupportedFormat" xml:space="preserve">
    <value>Unsupported format</value>
  </data>
  <data name="Error_LoadFailed" xml:space="preserve">
    <value>Failed to load file</value>
  </data>
  <data name="Error_SaveFailed" xml:space="preserve">
    <value>Failed to save file</value>
  </data>
  <data name="Error_ProcessingFailed" xml:space="preserve">
    <value>Failed to process image</value>
  </data>
  
  <!-- Dialog Titles -->
  <data name="Dialog_OpenFile" xml:space="preserve">
    <value>Open File</value>
  </data>
  <data name="Dialog_SaveFile" xml:space="preserve">
    <value>Save File</value>
  </data>
  <data name="Dialog_SelectFolder" xml:space="preserve">
    <value>Select Folder</value>
  </data>
  <data name="Dialog_Settings" xml:space="preserve">
    <value>Settings</value>
  </data>
  <data name="Dialog_About" xml:space="preserve">
    <value>About</value>
  </data>
  
  <!-- Settings -->
  <data name="Settings_General" xml:space="preserve">
    <value>General</value>
  </data>
  <data name="Settings_Interface" xml:space="preserve">
    <value>Interface</value>
  </data>
  <data name="Settings_Performance" xml:space="preserve">
    <value>Performance</value>
  </data>
  <data name="Settings_Language" xml:space="preserve">
    <value>Language</value>
  </data>
  <data name="Settings_Theme" xml:space="preserve">
    <value>Theme</value>
  </data>
  <data name="Settings_DarkTheme" xml:space="preserve">
    <value>Dark Theme</value>
  </data>
  <data name="Settings_LightTheme" xml:space="preserve">
    <value>Light Theme</value>
  </data>
  
  <!-- Export -->
  <data name="Export_Format" xml:space="preserve">
    <value>Format</value>
  </data>
  <data name="Export_Quality" xml:space="preserve">
    <value>Quality</value>
  </data>
  <data name="Export_Size" xml:space="preserve">
    <value>Size</value>
  </data>
  <data name="Export_Folder" xml:space="preserve">
    <value>Output Folder</value>
  </data>
  <data name="Export_Start" xml:space="preserve">
    <value>Start Export</value>
  </data>
  <data name="Export_Progress" xml:space="preserve">
    <value>Export Progress</value>
  </data>
  
  <!-- Library -->
  <data name="Library_Photos" xml:space="preserve">
    <value>Photos</value>
  </data>
  <data name="Library_Collections" xml:space="preserve">
    <value>Collections</value>
  </data>
  <data name="Library_Folders" xml:space="preserve">
    <value>Folders</value>
  </data>
  <data name="Library_Search" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="Library_Filter" xml:space="preserve">
    <value>Filter</value>
  </data>
  
  <!-- Image Info -->
  <data name="Info_Filename" xml:space="preserve">
    <value>Filename</value>
  </data>
  <data name="Info_Dimensions" xml:space="preserve">
    <value>Dimensions</value>
  </data>
  <data name="Info_FileSize" xml:space="preserve">
    <value>File Size</value>
  </data>
  <data name="Info_Format" xml:space="preserve">
    <value>Format</value>
  </data>
  <data name="Info_DateCreated" xml:space="preserve">
    <value>Date Created</value>
  </data>
  <data name="Info_DateModified" xml:space="preserve">
    <value>Date Modified</value>
  </data>
  
  <!-- Tooltips -->
  <data name="Tooltip_Undo" xml:space="preserve">
    <value>Undo last action</value>
  </data>
  <data name="Tooltip_Redo" xml:space="preserve">
    <value>Redo last action</value>
  </data>
  <data name="Tooltip_ZoomIn" xml:space="preserve">
    <value>Zoom in</value>
  </data>
  <data name="Tooltip_ZoomOut" xml:space="preserve">
    <value>Zoom out</value>
  </data>
  <data name="Tooltip_ZoomFit" xml:space="preserve">
    <value>Fit image to screen</value>
  </data>
  <data name="Tooltip_BeforeAfter" xml:space="preserve">
    <value>Compare before and after</value>
  </data>
  
  <!-- About -->
  <data name="About_Version" xml:space="preserve">
    <value>Version</value>
  </data>
  <data name="About_Copyright" xml:space="preserve">
    <value>All rights reserved</value>
  </data>
  <data name="About_Description" xml:space="preserve">
    <value>Professional photo editor with full Arabic language support</value>
  </data>
</root>
