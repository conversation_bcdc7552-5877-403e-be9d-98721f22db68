﻿#pragma checksum "..\..\..\..\..\Dialogs\ExportProgressDialog.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "8DA559D5ED140940A67E4973F96E9A477DF3CE8C"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace ELashraffyEditor.UI.Dialogs {
    
    
    /// <summary>
    /// ExportProgressDialog
    /// </summary>
    public partial class ExportProgressDialog : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 43 "..\..\..\..\..\Dialogs\ExportProgressDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar OverallProgressBar;
        
        #line default
        #line hidden
        
        
        #line 48 "..\..\..\..\..\Dialogs\ExportProgressDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OverallProgressText;
        
        #line default
        #line hidden
        
        
        #line 58 "..\..\..\..\..\Dialogs\ExportProgressDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentFileText;
        
        #line default
        #line hidden
        
        
        #line 65 "..\..\..\..\..\Dialogs\ExportProgressDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar CurrentFileProgressBar;
        
        #line default
        #line hidden
        
        
        #line 83 "..\..\..\..\..\Dialogs\ExportProgressDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CompletedCountText;
        
        #line default
        #line hidden
        
        
        #line 98 "..\..\..\..\..\Dialogs\ExportProgressDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FailedCountText;
        
        #line default
        #line hidden
        
        
        #line 113 "..\..\..\..\..\Dialogs\ExportProgressDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RemainingCountText;
        
        #line default
        #line hidden
        
        
        #line 132 "..\..\..\..\..\Dialogs\ExportProgressDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TimeRemainingText;
        
        #line default
        #line hidden
        
        
        #line 146 "..\..\..\..\..\Dialogs\ExportProgressDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LogTextBlock;
        
        #line default
        #line hidden
        
        
        #line 162 "..\..\..\..\..\Dialogs\ExportProgressDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PauseResumeButton;
        
        #line default
        #line hidden
        
        
        #line 168 "..\..\..\..\..\Dialogs\ExportProgressDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        
        #line 173 "..\..\..\..\..\Dialogs\ExportProgressDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/ELashraffyEditor.UI;V1.0.0.0;component/dialogs/exportprogressdialog.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Dialogs\ExportProgressDialog.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.OverallProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 2:
            this.OverallProgressText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.CurrentFileText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.CurrentFileProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 5:
            this.CompletedCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.FailedCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.RemainingCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.TimeRemainingText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.LogTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.PauseResumeButton = ((System.Windows.Controls.Button)(target));
            
            #line 166 "..\..\..\..\..\Dialogs\ExportProgressDialog.xaml"
            this.PauseResumeButton.Click += new System.Windows.RoutedEventHandler(this.PauseResumeButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 171 "..\..\..\..\..\Dialogs\ExportProgressDialog.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 177 "..\..\..\..\..\Dialogs\ExportProgressDialog.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

