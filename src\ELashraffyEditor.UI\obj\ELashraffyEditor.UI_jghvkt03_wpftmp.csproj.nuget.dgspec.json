{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\محرر ُ\\src\\ELashraffyEditor.UI\\ELashraffyEditor.UI.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\محرر ُ\\src\\ELashraffyEditor.Core\\ELashraffyEditor.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\محرر ُ\\src\\ELashraffyEditor.Core\\ELashraffyEditor.Core.csproj", "projectName": "ELashraffyEditor.Core", "projectPath": "C:\\Users\\<USER>\\Desktop\\محرر ُ\\src\\ELashraffyEditor.Core\\ELashraffyEditor.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\محرر ُ\\src\\ELashraffyEditor.Core\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net6.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[7.0.0, )"}, "SixLabors.ImageSharp": {"target": "Package", "version": "[3.0.2, )"}, "SixLabors.ImageSharp.Drawing": {"target": "Package", "version": "[2.0.1, )"}, "System.Text.Json": {"target": "Package", "version": "[7.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.412\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\محرر ُ\\src\\ELashraffyEditor.Scripting\\ELashraffyEditor.Scripting.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\محرر ُ\\src\\ELashraffyEditor.Scripting\\ELashraffyEditor.Scripting.csproj", "projectName": "ELashraffyEditor.Scripting", "projectPath": "C:\\Users\\<USER>\\Desktop\\محرر ُ\\src\\ELashraffyEditor.Scripting\\ELashraffyEditor.Scripting.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\محرر ُ\\src\\ELashraffyEditor.Scripting\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net6.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\محرر ُ\\src\\ELashraffyEditor.Core\\ELashraffyEditor.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\محرر ُ\\src\\ELashraffyEditor.Core\\ELashraffyEditor.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[7.0.0, )"}, "NLua": {"target": "Package", "version": "[1.7.0, )"}, "Python.Runtime": {"target": "Package", "version": "[2.7.9, )"}, "System.Text.Json": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.412\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\محرر ُ\\src\\ELashraffyEditor.UI\\ELashraffyEditor.UI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\محرر ُ\\src\\ELashraffyEditor.UI\\ELashraffyEditor.UI.csproj", "projectName": "ELashraffyEditor.UI", "projectPath": "C:\\Users\\<USER>\\Desktop\\محرر ُ\\src\\ELashraffyEditor.UI\\ELashraffyEditor.UI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\محرر ُ\\src\\ELashraffyEditor.UI\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\محرر ُ\\src\\ELashraffyEditor.Core\\ELashraffyEditor.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\محرر ُ\\src\\ELashraffyEditor.Core\\ELashraffyEditor.Core.csproj"}, "C:\\Users\\<USER>\\Desktop\\محرر ُ\\src\\ELashraffyEditor.Scripting\\ELashraffyEditor.Scripting.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\محرر ُ\\src\\ELashraffyEditor.Scripting\\ELashraffyEditor.Scripting.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.2.2, )"}, "MaterialDesignColors": {"target": "Package", "version": "[2.1.4, )"}, "MaterialDesignThemes": {"target": "Package", "version": "[4.9.0, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[7.0.1, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.NET.ILLink.Analyzers": {"suppressParent": "All", "target": "Package", "version": "[7.0.100-1.23401.1, )", "autoReferenced": true}, "Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[7.0.100-1.23401.1, )", "autoReferenced": true}, "Microsoft.Web.WebView2": {"target": "Package", "version": "[1.0.2045.28, )"}, "System.Text.Json": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.AspNetCore.App.Runtime.win-x64", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Crossgen2.win-x64", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Host.win-x64", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Runtime.win-x64", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Runtime.win-x64", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.412\\RuntimeIdentifierGraph.json"}}, "runtimes": {"win-x64": {"#import": []}}}}}