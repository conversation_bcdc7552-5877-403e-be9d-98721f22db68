// دليل المستخدم التفاعلي
// Interactive User Guide

class UserGuide {
    constructor() {
        this.currentSection = 'getting-started';
        this.currentLanguage = 'ar-SA';
        this.currentTheme = 'dark';
        this.searchIndex = [];
        this.readingSections = new Set();
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupNavigation();
        this.setupSearch();
        this.setupProgressTracking();
        this.buildSearchIndex();
        this.updateUI();
        
        console.log('User Guide initialized');
    }

    setupEventListeners() {
        // Theme toggle
        const themeBtn = document.getElementById('themeBtn');
        if (themeBtn) {
            themeBtn.addEventListener('click', () => this.toggleTheme());
        }

        // Language toggle
        const languageBtn = document.getElementById('languageBtn');
        if (languageBtn) {
            languageBtn.addEventListener('click', () => this.toggleLanguage());
        }

        // Search toggle
        const searchBtn = document.getElementById('searchBtn');
        if (searchBtn) {
            searchBtn.addEventListener('click', () => this.toggleSearch());
        }

        // Close search
        const closeSearchBtn = document.getElementById('closeSearchBtn');
        if (closeSearchBtn) {
            closeSearchBtn.addEventListener('click', () => this.closeSearch());
        }

        // Search input
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => this.performSearch(e.target.value));
            searchInput.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    this.closeSearch();
                }
            });
        }

        // Scroll tracking for progress
        window.addEventListener('scroll', () => this.updateProgress());
        
        // Hotspot interactions
        this.setupHotspots();
    }

    setupNavigation() {
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const section = link.getAttribute('data-section');
                this.showSection(section);
            });
        });

        // Handle hash changes
        window.addEventListener('hashchange', () => {
            const hash = window.location.hash.substring(1);
            if (hash) {
                this.showSection(hash);
            }
        });

        // Initial section from hash
        const initialHash = window.location.hash.substring(1);
        if (initialHash) {
            this.showSection(initialHash);
        }
    }

    setupSearch() {
        // Search functionality will be implemented here
    }

    setupProgressTracking() {
        // Track reading progress
        const sections = document.querySelectorAll('.guide-section');
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting && entry.intersectionRatio > 0.5) {
                    this.readingSections.add(entry.target.id);
                    this.updateProgressIndicator();
                }
            });
        }, {
            threshold: 0.5
        });

        sections.forEach(section => {
            observer.observe(section);
        });
    }

    setupHotspots() {
        const hotspots = document.querySelectorAll('.hotspot');
        hotspots.forEach(hotspot => {
            hotspot.addEventListener('click', () => {
                const area = hotspot.getAttribute('data-area');
                this.highlightInterfaceArea(area);
            });
        });
    }

    showSection(sectionId) {
        // Hide all sections
        document.querySelectorAll('.guide-section').forEach(section => {
            section.classList.remove('active');
        });

        // Show target section
        const targetSection = document.getElementById(sectionId);
        if (targetSection) {
            targetSection.classList.add('active');
            this.currentSection = sectionId;
            
            // Update navigation
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            
            const activeNavItem = document.querySelector(`[data-section="${sectionId}"]`)?.closest('.nav-item');
            if (activeNavItem) {
                activeNavItem.classList.add('active');
            }

            // Update URL hash
            window.history.pushState(null, null, `#${sectionId}`);
            
            // Scroll to top
            window.scrollTo({ top: 0, behavior: 'smooth' });
            
            console.log(`Switched to section: ${sectionId}`);
        }
    }

    toggleTheme() {
        this.currentTheme = this.currentTheme === 'dark' ? 'light' : 'dark';
        this.applyTheme();
    }

    applyTheme() {
        const body = document.body;
        const themeBtn = document.getElementById('themeBtn');
        
        if (this.currentTheme === 'dark') {
            body.classList.remove('light-theme');
            body.classList.add('dark-theme');
            if (themeBtn) {
                themeBtn.querySelector('.material-icons').textContent = 'dark_mode';
            }
        } else {
            body.classList.remove('dark-theme');
            body.classList.add('light-theme');
            if (themeBtn) {
                themeBtn.querySelector('.material-icons').textContent = 'light_mode';
            }
        }

        console.log(`Theme changed to: ${this.currentTheme}`);
    }

    toggleLanguage() {
        this.currentLanguage = this.currentLanguage === 'ar-SA' ? 'en-US' : 'ar-SA';
        this.updateLanguage();
    }

    updateLanguage() {
        const html = document.documentElement;
        const isArabic = this.currentLanguage.startsWith('ar');
        
        html.setAttribute('lang', this.currentLanguage);
        html.setAttribute('dir', isArabic ? 'rtl' : 'ltr');
        
        // Update font family
        document.body.style.fontFamily = isArabic ? 
            "var(--font-arabic)" : "var(--font-english)";

        console.log(`Language changed to: ${this.currentLanguage}`);
    }

    toggleSearch() {
        const searchBar = document.getElementById('searchBar');
        const searchInput = document.getElementById('searchInput');
        
        if (searchBar && searchInput) {
            searchBar.classList.toggle('hidden');
            if (!searchBar.classList.contains('hidden')) {
                searchInput.focus();
            }
        }
    }

    closeSearch() {
        const searchBar = document.getElementById('searchBar');
        const searchInput = document.getElementById('searchInput');
        
        if (searchBar && searchInput) {
            searchBar.classList.add('hidden');
            searchInput.value = '';
            this.clearSearchResults();
        }
    }

    performSearch(query) {
        if (!query.trim()) {
            this.clearSearchResults();
            return;
        }

        const results = this.searchContent(query);
        this.displaySearchResults(results);
    }

    searchContent(query) {
        const results = [];
        const searchTerm = query.toLowerCase();
        
        // Search through all sections
        document.querySelectorAll('.guide-section').forEach(section => {
            const sectionId = section.id;
            const sectionTitle = section.querySelector('h2')?.textContent || '';
            const sectionContent = section.textContent.toLowerCase();
            
            if (sectionContent.includes(searchTerm)) {
                // Find specific matches within the section
                const paragraphs = section.querySelectorAll('p, h3, h4');
                paragraphs.forEach(element => {
                    const text = element.textContent.toLowerCase();
                    if (text.includes(searchTerm)) {
                        results.push({
                            section: sectionId,
                            title: sectionTitle,
                            content: element.textContent,
                            element: element
                        });
                    }
                });
            }
        });

        return results;
    }

    displaySearchResults(results) {
        // This would display search results in a dropdown or overlay
        console.log('Search results:', results);
    }

    clearSearchResults() {
        // Clear any displayed search results
        console.log('Search results cleared');
    }

    buildSearchIndex() {
        // Build search index for faster searching
        document.querySelectorAll('.guide-section').forEach(section => {
            const sectionData = {
                id: section.id,
                title: section.querySelector('h2')?.textContent || '',
                content: section.textContent,
                keywords: this.extractKeywords(section.textContent)
            };
            this.searchIndex.push(sectionData);
        });
    }

    extractKeywords(text) {
        // Extract keywords from text for better search
        const words = text.toLowerCase()
            .replace(/[^\w\s\u0600-\u06FF]/g, '') // Keep Arabic and English characters
            .split(/\s+/)
            .filter(word => word.length > 2);
        
        return [...new Set(words)]; // Remove duplicates
    }

    highlightInterfaceArea(area) {
        // Highlight specific interface areas
        console.log(`Highlighting interface area: ${area}`);
        
        // Add visual highlighting effect
        const hotspot = document.querySelector(`[data-area="${area}"]`);
        if (hotspot) {
            hotspot.style.animation = 'pulse 1s ease-in-out 3';
        }
    }

    updateProgress() {
        // Update reading progress based on scroll position
        const scrollTop = window.pageYOffset;
        const docHeight = document.documentElement.scrollHeight - window.innerHeight;
        const scrollPercent = (scrollTop / docHeight) * 100;
        
        this.updateProgressIndicator(Math.min(scrollPercent, 100));
    }

    updateProgressIndicator(progress = null) {
        const progressFill = document.querySelector('.progress-fill');
        const progressText = document.querySelector('.progress-text');
        
        if (progress === null) {
            // Calculate progress based on sections read
            const totalSections = document.querySelectorAll('.guide-section').length;
            const readSections = this.readingSections.size;
            progress = (readSections / totalSections) * 100;
        }
        
        if (progressFill && progressText) {
            progressFill.style.width = `${progress}%`;
            progressText.textContent = `${Math.round(progress)}%`;
        }
    }

    updateUI() {
        this.applyTheme();
        this.updateLanguage();
        this.updateProgressIndicator();
    }

    // Public methods for external interaction
    navigateToSection(sectionId) {
        this.showSection(sectionId);
    }

    getCurrentSection() {
        return this.currentSection;
    }

    getReadingProgress() {
        const totalSections = document.querySelectorAll('.guide-section').length;
        const readSections = this.readingSections.size;
        return (readSections / totalSections) * 100;
    }
}

// Global functions for button interactions
function showSection(sectionId) {
    if (window.userGuide) {
        window.userGuide.navigateToSection(sectionId);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.userGuide = new UserGuide();
});

// Handle messages from parent application if running in WebView2
if (window.chrome && window.chrome.webview) {
    window.chrome.webview.addEventListener('message', (event) => {
        const message = event.data;
        
        switch (message.type) {
            case 'navigateToSection':
                if (window.userGuide && message.section) {
                    window.userGuide.navigateToSection(message.section);
                }
                break;
                
            case 'setTheme':
                if (window.userGuide && message.theme) {
                    window.userGuide.currentTheme = message.theme;
                    window.userGuide.applyTheme();
                }
                break;
                
            case 'setLanguage':
                if (window.userGuide && message.language) {
                    window.userGuide.currentLanguage = message.language;
                    window.userGuide.updateLanguage();
                }
                break;
        }
    });
}
