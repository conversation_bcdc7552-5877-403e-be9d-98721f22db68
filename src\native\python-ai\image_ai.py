"""
محرر الأشرافي - وحدة الذكاء الاصطناعي لمعالجة الصور
ELashrafy Editor - AI Image Processing Module

هذه الوحدة توفر ميزات الذكاء الاصطناعي المتقدمة لتحسين الصور
This module provides advanced AI features for image enhancement
"""

import cv2
import numpy as np
from skimage import exposure, filters, restoration
from sklearn.cluster import KMeans
import json
import sys
import traceback
from typing import Dict, List, Tuple, Optional, Any

class ImageAI:
    """فئة الذكاء الاصطناعي لمعالجة الصور"""
    
    def __init__(self):
        """تهيئة فئة الذكاء الاصطناعي"""
        self.models_loaded = False
        self.face_cascade = None
        self.eye_cascade = None
        
    def load_models(self) -> bool:
        """تحميل نماذج الذكاء الاصطناعي"""
        try:
            # تحميل نماذج كشف الوجه
            self.face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
            self.eye_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_eye.xml')
            
            self.models_loaded = True
            return True
        except Exception as e:
            print(f"خطأ في تحميل النماذج / Error loading models: {e}")
            return False
    
    def auto_enhance_smart(self, image_path: str) -> Dict[str, Any]:
        """تحسين ذكي تلقائي للصورة"""
        try:
            # قراءة الصورة
            image = cv2.imread(image_path)
            if image is None:
                return {"success": False, "error": "فشل في قراءة الصورة / Failed to read image"}
            
            # تحليل الصورة
            analysis = self._analyze_image(image)
            
            # تطبيق التحسينات بناءً على التحليل
            adjustments = self._calculate_smart_adjustments(image, analysis)
            
            return {
                "success": True,
                "adjustments": adjustments,
                "analysis": analysis
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"خطأ في التحسين التلقائي / Auto enhancement error: {str(e)}"
            }
    
    def detect_faces(self, image_path: str) -> Dict[str, Any]:
        """كشف الوجوه في الصورة"""
        try:
            if not self.models_loaded:
                if not self.load_models():
                    return {"success": False, "error": "فشل في تحميل نماذج كشف الوجه"}
            
            image = cv2.imread(image_path)
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # كشف الوجوه
            faces = self.face_cascade.detectMultiScale(gray, 1.1, 4)
            
            face_data = []
            for (x, y, w, h) in faces:
                face_data.append({
                    "x": int(x),
                    "y": int(y),
                    "width": int(w),
                    "height": int(h),
                    "confidence": 0.8  # تقدير الثقة
                })
            
            return {
                "success": True,
                "faces": face_data,
                "count": len(face_data)
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"خطأ في كشف الوجوه / Face detection error: {str(e)}"
            }
    
    def enhance_portrait(self, image_path: str) -> Dict[str, Any]:
        """تحسين الصور الشخصية"""
        try:
            image = cv2.imread(image_path)
            
            # كشف الوجوه أولاً
            faces_result = self.detect_faces(image_path)
            if not faces_result["success"] or faces_result["count"] == 0:
                # إذا لم يتم العثور على وجوه، استخدم التحسين العام
                return self.auto_enhance_smart(image_path)
            
            # تحسين خاص بالصور الشخصية
            adjustments = {
                "brightness": 5,      # إضاءة خفيفة
                "contrast": 10,       # تباين معتدل
                "saturation": 8,      # تشبع طفيف
                "shadows": 15,        # رفع الظلال
                "highlights": -8,     # تقليل الإضاءات العالية
                "clarity": 12,        # وضوح للتفاصيل
                "vibrance": 10,       # حيوية الألوان
                "skin_smoothing": 0.3 # نعومة البشرة
            }
            
            return {
                "success": True,
                "adjustments": adjustments,
                "faces": faces_result["faces"],
                "type": "portrait"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"خطأ في تحسين الصورة الشخصية / Portrait enhancement error: {str(e)}"
            }
    
    def color_correction_auto(self, image_path: str) -> Dict[str, Any]:
        """تصحيح الألوان التلقائي"""
        try:
            image = cv2.imread(image_path)
            
            # تحليل توازن الألوان
            color_analysis = self._analyze_color_balance(image)
            
            # حساب التصحيحات المطلوبة
            corrections = {
                "temperature": color_analysis["temperature_shift"],
                "tint": color_analysis["tint_shift"],
                "white_balance": color_analysis["white_balance_correction"]
            }
            
            return {
                "success": True,
                "corrections": corrections,
                "analysis": color_analysis
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"خطأ في تصحيح الألوان / Color correction error: {str(e)}"
            }
    
    def noise_reduction_ai(self, image_path: str, strength: float = 0.5) -> Dict[str, Any]:
        """تقليل الضوضاء بالذكاء الاصطناعي"""
        try:
            image = cv2.imread(image_path)
            
            # تحليل نوع ومستوى الضوضاء
            noise_analysis = self._analyze_noise(image)
            
            # تطبيق تقليل الضوضاء المناسب
            if noise_analysis["type"] == "gaussian":
                # ضوضاء غاوسية
                denoised = cv2.bilateralFilter(image, 9, 75, 75)
            elif noise_analysis["type"] == "salt_pepper":
                # ضوضاء ملح وفلفل
                denoised = cv2.medianBlur(image, 5)
            else:
                # ضوضاء عامة
                denoised = restoration.denoise_nl_means(
                    image, 
                    patch_size=7, 
                    patch_distance=11, 
                    h=0.1 * strength
                )
                denoised = (denoised * 255).astype(np.uint8)
            
            # حفظ النتيجة مؤقتاً
            temp_path = image_path.replace('.', '_denoised.')
            cv2.imwrite(temp_path, denoised)
            
            return {
                "success": True,
                "output_path": temp_path,
                "noise_analysis": noise_analysis,
                "strength_used": strength
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"خطأ في تقليل الضوضاء / Noise reduction error: {str(e)}"
            }
    
    def _analyze_image(self, image: np.ndarray) -> Dict[str, Any]:
        """تحليل شامل للصورة"""
        # تحليل الهيستوجرام
        hist_b = cv2.calcHist([image], [0], None, [256], [0, 256])
        hist_g = cv2.calcHist([image], [1], None, [256], [0, 256])
        hist_r = cv2.calcHist([image], [2], None, [256], [0, 256])
        
        # حساب الإحصائيات
        brightness = np.mean(cv2.cvtColor(image, cv2.COLOR_BGR2GRAY))
        contrast = np.std(cv2.cvtColor(image, cv2.COLOR_BGR2GRAY))
        
        # تحليل التوزيع
        dark_pixels = np.sum(hist_b[:85]) + np.sum(hist_g[:85]) + np.sum(hist_r[:85])
        bright_pixels = np.sum(hist_b[170:]) + np.sum(hist_g[170:]) + np.sum(hist_r[170:])
        total_pixels = image.shape[0] * image.shape[1] * 3
        
        return {
            "brightness": float(brightness),
            "contrast": float(contrast),
            "dark_ratio": float(dark_pixels / total_pixels),
            "bright_ratio": float(bright_pixels / total_pixels),
            "is_underexposed": brightness < 80,
            "is_overexposed": brightness > 180,
            "is_low_contrast": contrast < 30
        }
    
    def _calculate_smart_adjustments(self, image: np.ndarray, analysis: Dict[str, Any]) -> Dict[str, float]:
        """حساب التعديلات الذكية بناءً على التحليل"""
        adjustments = {
            "brightness": 0.0,
            "contrast": 0.0,
            "saturation": 0.0,
            "exposure": 0.0,
            "shadows": 0.0,
            "highlights": 0.0,
            "clarity": 0.0,
            "vibrance": 0.0
        }
        
        # تعديل السطوع
        if analysis["is_underexposed"]:
            adjustments["brightness"] = 15.0
            adjustments["shadows"] = 20.0
        elif analysis["is_overexposed"]:
            adjustments["brightness"] = -10.0
            adjustments["highlights"] = -15.0
        
        # تعديل التباين
        if analysis["is_low_contrast"]:
            adjustments["contrast"] = 15.0
            adjustments["clarity"] = 10.0
        
        # تحسين عام
        adjustments["vibrance"] = 8.0
        adjustments["saturation"] = 5.0
        
        return adjustments
    
    def _analyze_color_balance(self, image: np.ndarray) -> Dict[str, Any]:
        """تحليل توازن الألوان"""
        # حساب متوسط كل قناة لونية
        b_mean = np.mean(image[:, :, 0])
        g_mean = np.mean(image[:, :, 1])
        r_mean = np.mean(image[:, :, 2])
        
        # حساب الانحراف عن الرمادي المحايد
        gray_target = (b_mean + g_mean + r_mean) / 3
        
        temperature_shift = (r_mean - b_mean) / gray_target * 100
        tint_shift = (g_mean - (r_mean + b_mean) / 2) / gray_target * 100
        
        return {
            "temperature_shift": float(temperature_shift),
            "tint_shift": float(tint_shift),
            "white_balance_correction": abs(temperature_shift) > 10 or abs(tint_shift) > 10,
            "color_cast": "warm" if temperature_shift > 10 else "cool" if temperature_shift < -10 else "neutral"
        }
    
    def _analyze_noise(self, image: np.ndarray) -> Dict[str, Any]:
        """تحليل الضوضاء في الصورة"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # حساب التباين المحلي لكشف الضوضاء
        laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()
        
        # تحديد نوع الضوضاء
        if laplacian_var < 100:
            noise_type = "low"
        elif laplacian_var < 500:
            noise_type = "gaussian"
        else:
            noise_type = "salt_pepper"
        
        return {
            "type": noise_type,
            "level": float(laplacian_var),
            "severity": "high" if laplacian_var > 500 else "medium" if laplacian_var > 100 else "low"
        }

def main():
    """الدالة الرئيسية للاستدعاء من C#"""
    if len(sys.argv) < 3:
        print(json.dumps({"success": False, "error": "معاملات غير كافية / Insufficient parameters"}))
        return
    
    try:
        operation = sys.argv[1]
        image_path = sys.argv[2]
        
        ai = ImageAI()
        
        if operation == "auto_enhance":
            result = ai.auto_enhance_smart(image_path)
        elif operation == "detect_faces":
            result = ai.detect_faces(image_path)
        elif operation == "enhance_portrait":
            result = ai.enhance_portrait(image_path)
        elif operation == "color_correction":
            result = ai.color_correction_auto(image_path)
        elif operation == "noise_reduction":
            strength = float(sys.argv[3]) if len(sys.argv) > 3 else 0.5
            result = ai.noise_reduction_ai(image_path, strength)
        else:
            result = {"success": False, "error": f"عملية غير معروفة / Unknown operation: {operation}"}
        
        print(json.dumps(result, ensure_ascii=False, indent=2))
        
    except Exception as e:
        error_result = {
            "success": False,
            "error": f"خطأ في التنفيذ / Execution error: {str(e)}",
            "traceback": traceback.format_exc()
        }
        print(json.dumps(error_result, ensure_ascii=False, indent=2))

if __name__ == "__main__":
    main()
