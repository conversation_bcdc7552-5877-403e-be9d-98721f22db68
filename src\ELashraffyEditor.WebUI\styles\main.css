/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Dark Theme Colors */
    --primary-bg: #1e1e1e;
    --secondary-bg: #2d2d30;
    --tertiary-bg: #3e3e42;
    --quaternary-bg: #4a4a4f;
    
    --primary-text: #ffffff;
    --secondary-text: #b0b0b0;
    --tertiary-text: #808080;
    --disabled-text: #606060;
    
    --accent-color: #673ab7;
    --accent-hover: #7c4dff;
    --accent-pressed: #5e35b1;
    
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --error-color: #f44336;
    --info-color: #2196f3;
    
    --border-color: #404040;
    --separator-color: #333333;
    --highlight-color: #2196f3;
    --selection-color: #3f51b5;
    
    /* Spacing */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    
    /* Border Radius */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    
    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.2);
    
    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.25s ease;
    --transition-slow: 0.4s ease;
    
    /* Fonts */
    --font-arabic: 'Tajawal', 'Tahoma', sans-serif;
    --font-english: 'Inter', 'Segoe UI', sans-serif;
}

/* Light Theme */
body.light-theme {
    --primary-bg: #ffffff;
    --secondary-bg: #f8f8f8;
    --tertiary-bg: #f0f0f0;
    --quaternary-bg: #e8e8e8;
    
    --primary-text: #000000;
    --secondary-text: #606060;
    --tertiary-text: #808080;
    --disabled-text: #a0a0a0;
    
    --border-color: #e0e0e0;
    --separator-color: #d0d0d0;
}

/* Base Typography */
body {
    font-family: var(--font-arabic);
    font-size: 14px;
    line-height: 1.5;
    color: var(--primary-text);
    background-color: var(--primary-bg);
    transition: background-color var(--transition-normal), color var(--transition-normal);
}

[lang="en"], .english {
    font-family: var(--font-english);
}

h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
}

h1 { font-size: 24px; }
h2 { font-size: 20px; }
h3 { font-size: 18px; }
h4 { font-size: 16px; }
h5 { font-size: 14px; }
h6 { font-size: 12px; }

/* Layout */
.container {
    display: grid;
    grid-template-areas: 
        "header header"
        "sidebar main"
        "footer footer";
    grid-template-columns: 250px 1fr;
    grid-template-rows: auto 1fr auto;
    min-height: 100vh;
}

/* Header */
.header {
    grid-area: header;
    background-color: var(--secondary-bg);
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-md);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.logo .material-icons {
    font-size: 32px;
    color: var(--accent-color);
}

.logo h1 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
}

.logo .subtitle {
    font-size: 14px;
    color: var(--secondary-text);
    font-family: var(--font-english);
}

.language-toggle {
    display: flex;
    gap: var(--spacing-sm);
}

/* Sidebar Navigation */
.sidebar {
    grid-area: sidebar;
    background-color: var(--secondary-bg);
    border-right: 1px solid var(--border-color);
    padding: var(--spacing-md);
}

.nav-menu {
    list-style: none;
}

.nav-item {
    margin-bottom: var(--spacing-xs);
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--secondary-text);
    text-decoration: none;
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
}

.nav-link:hover {
    background-color: var(--tertiary-bg);
    color: var(--primary-text);
}

.nav-item.active .nav-link {
    background-color: var(--accent-color);
    color: white;
}

.nav-link .material-icons {
    font-size: 20px;
}

/* Main Content */
.main-content {
    grid-area: main;
    padding: var(--spacing-lg);
    overflow-y: auto;
}

.settings-section {
    display: none;
}

.settings-section.active {
    display: block;
}

.settings-group {
    background-color: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.settings-group h3 {
    color: var(--accent-color);
    margin-bottom: var(--spacing-md);
    font-size: 16px;
}

.setting-item {
    margin-bottom: var(--spacing-md);
}

.setting-item:last-child {
    margin-bottom: 0;
}

.setting-item label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: 500;
    color: var(--primary-text);
}

/* Form Controls */
.form-control {
    width: 100%;
    max-width: 300px;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    background-color: var(--tertiary-bg);
    color: var(--primary-text);
    font-size: 14px;
    transition: border-color var(--transition-fast);
}

.form-control:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(103, 58, 183, 0.2);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--radius-sm);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
}

.btn-primary {
    background-color: var(--accent-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--accent-hover);
}

.btn-secondary {
    background-color: var(--tertiary-bg);
    color: var(--primary-text);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background-color: var(--quaternary-bg);
}

.btn-icon {
    width: 40px;
    height: 40px;
    padding: 0;
    border: none;
    border-radius: var(--radius-sm);
    background-color: transparent;
    color: var(--secondary-text);
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-icon:hover {
    background-color: var(--tertiary-bg);
    color: var(--primary-text);
}

/* Checkbox */
.checkbox-label {
    display: flex !important;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    margin-bottom: 0 !important;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-sm);
    background-color: var(--tertiary-bg);
    position: relative;
    transition: all var(--transition-fast);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

/* Slider */
.slider {
    width: 100%;
    max-width: 300px;
    height: 6px;
    border-radius: 3px;
    background-color: var(--tertiary-bg);
    outline: none;
    -webkit-appearance: none;
    margin-right: var(--spacing-sm);
}

.slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: var(--accent-color);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.slider::-webkit-slider-thumb:hover {
    background-color: var(--accent-hover);
    transform: scale(1.1);
}

.slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: var(--accent-color);
    cursor: pointer;
    border: none;
}

.slider-value {
    display: inline-block;
    min-width: 50px;
    font-weight: 500;
    color: var(--accent-color);
}

/* Footer */
.footer {
    grid-area: footer;
    background-color: var(--secondary-bg);
    border-top: 1px solid var(--border-color);
    padding: var(--spacing-md);
}

.footer-content {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-sm);
}

/* About Section */
.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
}

.app-info h3 {
    color: var(--accent-color);
    margin-bottom: var(--spacing-md);
}

.tech-stack h4 {
    color: var(--accent-color);
    margin-bottom: var(--spacing-sm);
}

.tech-stack ul {
    list-style: none;
    padding-left: var(--spacing-md);
}

.tech-stack li {
    margin-bottom: var(--spacing-xs);
    position: relative;
}

.tech-stack li::before {
    content: '•';
    color: var(--accent-color);
    position: absolute;
    left: -var(--spacing-md);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        grid-template-areas: 
            "header"
            "main"
            "footer";
        grid-template-columns: 1fr;
    }
    
    .sidebar {
        display: none;
    }
    
    .about-content {
        grid-template-columns: 1fr;
    }
    
    .header-content {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.settings-section.active {
    animation: fadeIn var(--transition-normal);
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background-color: var(--secondary-bg);
}

::-webkit-scrollbar-thumb {
    background-color: var(--tertiary-bg);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background-color: var(--quaternary-bg);
}
